/* --------------------------- (C) COPYRIGHT 2021 Fortiortech ShenZhen -----------------------------
    File Name      : FLASH.h
    Author         : Fortiortech  Appliction Team
    Version        : V1.0
    Date           : 2021-07-24
    Description    : This file contains flash变量和函数的定义 parameter used for Motor Control.
----------------------------------------------------------------------------------------------------
                                       All Rights Reserved
------------------------------------------------------------------------------------------------- */

/* Define to prevent recursive inclusion -------------------------------------------------------- */
/******************************************************************************///Including Header Files
#include <FU65xx_2.h>


#ifndef __FLASH_H__
#define __FLASH_H__
#define USERCODEADDRESS 0X3F00

typedef struct
{	
	uint8   FlashRead; 
	uint8   FlashWrite;
	uint16  FlashRead2Byte;
	uint16  FlashWrite2Byte;
    
} MCUFlash;

extern MCUFlash xdata FlashTest;                                                        //flash数据测试
extern uint16 Get2ByteFromFlash(uint8 xdata *BlockStartAddr);                          //2字节的flash读取函数   
extern uint8 Write2Byte2Flash(uint8 xdata *BlockStartAddr,uint16 NewData2Flash);      //2字节的flash写入函数
extern void WriteOrReadFlashTest(uint8 xdata *BlockStartAddr,uint16 NewData2Flash);  //读写函数定义

extern  void Flash_Sector_Erase(uint8 xdata *FlashAddress);                         //自擦除函数定义  
extern  void Flash_Sector_Write(uint8 xdata *FlashAddress, uint8 FlashData);        //自写入函数定义

#endif

