/* --------------------------- (C) COPYRIGHT 2021 Fortiortech ShenZhen -----------------------------
    File Name      : FLASH.h
    Author         : Fortiortech  Appliction Team
    Version        : V1.0
    Date           : 2021-07-24
    Description    : This file contains flash变量和函数的定义 parameter used for Motor Control.
----------------------------------------------------------------------------------------------------
                                       All Rights Reserved
------------------------------------------------------------------------------------------------- */

/* Define to prevent recursive inclusion -------------------------------------------------------- */
///******************************************************************************///Including Header Files
#include <FU65xx_2.h>


//#ifndef __FLASH_H__
//#define __FLASH_H__
//#define USERCODEADDRESS 0X3F00

////typedef struct
////{	
////	uint8   FlashRead; 
////	uint8   FlashWrite;
////	uint16  FlashRead2Byte;
////	uint16  FlashWrite2Byte;
////    
////} MCUFlash;

//extern MCUFlash xdata FlashTest;                                                        //flash数据测试
//extern uint16 Get2ByteFromFlash(uint8 xdata *BlockStartAddr);                          //2字节的flash读取函数   
//extern uint8 Write2Byte2Flash(uint8 xdata *BlockStartAddr,uint16 NewData2Flash);      //2字节的flash写入函数
//extern void WriteOrReadFlashTest(uint8 xdata *BlockStartAddr,uint16 NewData2Flash);  //读写函数定义

//extern  void Flash_Sector_Erase(uint8 xdata *FlashAddress);                         //自擦除函数定义  
//extern  void Flash_Sector_Write(uint8 xdata *FlashAddress, uint8 FlashData);        //自写入函数定义

//#endif


/************************ (C) COPYRIGHT 2015 FT *******************************
* File Name          : 
* Author             : Application Team  Tom.wang 
* Version            : V2.0.0
* Date               : 06/15/2015
* Description        : 
********************************************************************************
/*******************************************************************************
* All Rights Reserved
*******************************************************************************/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __FLASH_H
#define __FLASH_H
#define     __I     volatile const    /*!< defines 'read only' permissions      */
#define     __O     volatile          /*!< defines 'write only' permissions     */
#define     __IO    volatile          /*!< defines 'read / write' permissions   */

#define   START_WriteADDRESS    0x5000             //写入首地址
#define   START_WriteADDRESS_1  0x5800             //写入首地址
#define   START_WriteADDRESS_2  0x6000             //写入首地址
#define   START_WriteADDRESS_3  0x6800             //写入首地址
#define   START_WriteADDRESS_4  0x7000             //写入首地址
#define   START_WriteADDRESS_5  0x7800             //写入首地址

#define   Write_Lenght        2                  //写入字节长度
#define   Verify_Bit          Write_Lenght -1    //Data数值最后位为校验位
#define   Verify_Ture         0xFF               //校验正确数值
#define   Verify_Error        0x55               //校验错误数值

typedef struct
{
	uint16 WriteAddress;     // 写入地址
	uint16 WriteAddress_Top;  // 读取地址
	uint8 WriteValue[8];    // Flash 写入数组
	uint16 ReadAddress;      // 读取地址
	uint8 ReadValue[8];     // ROM 读出值
	uint8 FlagFlashWrite;   // 写标志位
	uint8 FlagFlashWrite_Start;   // 写标志位
	uint8 Flag_FlashErase;  // Flash擦除标志位
} ROM_TypeDef;

extern ROM_TypeDef xdata  FlashData;  
extern uint8  ReadFlashValue(uint16 ReadAddress);
extern uint8  Flash_WriteValue(uint16 WriteAddress ,uint8 Length,uint8 * str);
extern uint16 GetWrite_Black_Addr(unsigned short FlashAddress, uint8 Write_Length);
extern void   ReadFromFlash(unsigned short BlockStartAddr, uint8 Length, uint8 * str);
extern void Flash_Erase(void);
extern void   Save_KeyValue(void);
#endif