/**
 * @file	 FU6522_Type.h
 * @version	 V1.0.0
 * <AUTHOR> Hardware Team
 * @date	 2021-07-15	20:54:16
 * @brief	 This file contains	...
 * 
 * @copyright Copyright(C) 2022, Fortior Technology	Co., Ltd. All rights reserved.
 */

#ifndef	__FU65xx_2_Type_H__
#define	__FU65xx_2_Type_H__

// Include external	header file.
#include <ctype.h> 
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif


#define	_I								volatile const //< Defines 'read only' permissions
#define	_O								volatile	   //< Defines 'write only'	permissions
#define	_IO								volatile	   //< Defines 'read&write'	permissions

//#define bool							  bit
//#define false							  (0)
//#define true							  (!false)

typedef	unsigned char					uint8;
typedef	unsigned short					uint16;
typedef	unsigned long					uint32;
typedef	long							int32;
typedef	short							int16;
typedef	char							int8;

typedef	enum{DISABLE = 0, ENABLE}		ebool;
//typedef enum{false = 0, true}			  bool;


#ifdef __cplusplus
}
#endif

#endif	//__FU6522_TYPE_H__


/*** (C) Copyright 2022	Fortior	Technology Co.,	Ltd. ***/
