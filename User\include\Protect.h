/**
 * @copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
 * @file      Protect.h
 * <AUTHOR>
 * @note      Last modify author is <PERSON>
 * @since     2022-07-01
 * @date      2022-07-14
 * @brief     This file contains protection parameter used for Motor Control. 
 */
 

/* Define to prevent recursive inclusion -------------------------------------------------------- */
#ifndef __PROTECT_H_
#define __PROTECT_H_


/*保护参数设置--------------------------------------------------------------------------------------------*/

/* Faults processing Enable */
#define OC_SW_ProtectEn                     (1)                                     // 软件过流保护使能： 0,不使能；1，使能
#define OV_ProtectEn                        (1)                                     // 过欠压保护使能：   0,不使能；1，使能
#define LP_ProtectEn                        (1)                                     // 缺相保护使能：     0,不使能；1，使能
#define OT_ProtectEn                        (0)                                     // 过温保护使能：     0,不使能；1，使能
#define Stall_ProtectEn                     (1)                                     // 堵转失速保护使能： 0,不使能；1，使能
#define OP_ProtectEn                        (1)                                     ///< 功率保护，0,不使能；1，使能
#define GetCurrentOffsetEnable              (1)                                     // 偏置电压保护，0,不使能；1，使能

/*  保护重启参数设置  */
#define OC_RecoveryTimes                    (0)                                  // 重启次数,设定值<255 达到重启次数后不再重启，设定值>=255，一直重启
#define OC_RecoveryDelayTime                (5000)                               // (ms)重启延迟时间

#define OV_RecoveryTimes                    (255)                                   // 重启次数,设定值<255 达到重启次数后不再重启，设定值>=255，一直重启
#define OV_RecoveryDelayTime                (1200)                                // (ms)欠过压保护恢复时间
#define OV_RecoveryDelayTime1               (200)                                ///< (5ms) 欠过压保护恢复时间，过零判断用于快速插拔电开关机
#define OV_RecoveryDelayTime2               (1000)                               ///< (5ms) 过压保护恢复时间
 
#define LP_RecoveryTimes                    (0)                                   // 重启次数,设定值<255 达到重启次数后不再重启，设定值>=255，一直重启
#define LP_RecoveryDelayTime                (500)                                // (ms)重启延迟时间

#define OT_RecoveryTimes                    (0)                                   // 重启次数,设定值<255 达到重启次数后不再重启，设定值>=255，一直重启
#define OT_RecoveryDelayTime                (500)                                // (ms)重启延迟时间

#define Stall_RecoveryTimes                 (0)                                   // 重启次数,设定值<255 达到重启次数后不再重启，设定值>=255，一直重启
#define Stall_RecoveryDelayTime             (500)                                // (ms)重启延迟时间

#define OP_RecoveryTimes                    (0)                                   // 重启次数,设定值<255 达到重启次数后不再重启，设定值>=255，一直重启
#define OP_RecoveryDelayTime                (500)                                // (ms)重启延迟时间
/* HW current protection */
#define HW_OC_FO_MODE                       (1)                                     ///< 硬件FO过流保护使能，适用于IPM有FO保护的场合
#define HW_OC_CMP_MODE                      (2)                                     ///< 硬件CMP比较过流保护使能，适用于MOS管应用场合
#define HW_OC_FOCMP_MODE                    (3)                                     ///< 硬件CMP比较和FO过流保护都使能
#define HW_OC_DISABLE                       (4)                                     ///< 硬件过流保护禁止，用于测试
#define HW_OC_MODE                          (HW_OC_CMP_MODE)                         ///< 硬件过流保护实现方式

/* 硬件过流保护比较值来源 */
#define COMPARE_DAC_MODE                    (0)                                     ///< DAC设置硬件过流值
#define COMPARE_HW_MODE                     (1)                                     ///< 硬件设置硬件过流值
#define COMPARE_MODE                        (COMPARE_DAC_MODE)                      ///< 硬件过流值的来源
#define HWOCValue                           (5.0)                                  ///< (A) DAC模式下的硬件过流值

/* 软件过流保护参数设置 */
#define SW_OC_CurrentVal                    I_Value(5.0)                            ///< (A)软件过流值
#define SW_OC_DectTime                      (10)                                    ///< (ms)软件过流检测时间

/* -----偏置电压保护----- */
 #define GetCurrentOffsetValue              _Q14(0.05)                              // (单位:100%)偏置电压保护误差范围，超过该范围保护   
 
/* -----功率保护----- */
 #define  OverPowerValue                     (18000)                           ///<功率保护阈值
 
/* 直流母线电压保护参数设置值 */

#define OVER_VOLTAGE_DECTTIME               (300)                                   ///< (ms) 直流母线电压过压检测保持时间
#define UNDER_VOLTAGE_DECTTIME              (300)                                   ///< (ms) 直流母线电压欠压检测保持时间

#define OVER_VOLTAGE_PROTECT                UDC_Value(400)                          ///< (V) 直流母线电压过压保护值
#define UNDER_VOLTAGE_PROTECT               UDC_Value(160)                           ///< (V) 直流母线电压欠压保护值

#define OVER_VOLTAGE_RECOVER                UDC_Value(380)                          ///< (V) 直流母线电压过压保护恢复值            
#define UNDER_VOLTAGE_RECOVER               UDC_Value(250)                         ///< (V) 直流母线电压欠压保护恢复值

/* 堵转保护参数设置值 */
#define STALL_SPEED_MAX                     S_Value(120000)
#define STALL_SPEED_MIN                     S_Value(20000)

#define EsThresholdValueL                   (1000.0)
#define EsThresholdValueH                   (2000.0)
#define EsThresholdSpeed                    S_Value(65000)                          ///< (RPM) 电机转速


/* 缺相保护参数设置值 */
#define LP_NoLoadCurrentValue               I_Value(0.1)                          ///< (A) 相电流峰值小于该电流值则不进行缺相判断
#define LP_DectDealyTIME                    (500)                                 ///< (ms) 启动延迟检测时间，用于屏蔽启动阶段
#define LP_DectCycleTIME                    (40)                                  ///< (ms) 峰值检测周期时间,需满足覆盖至少一个完整电周期

/* -----NTC过温保护----- */
#define TemperatureProtectTime 			(2000)									     // (ms)温度保护检测时间
#define OVER_Temperature 		        Tempera_Value(1.0)						     // 过温保护阈值，根据NTC曲线设定，10K上拉电阻，80℃
#define UNDER_Temperature          		Tempera_Value(2.23)						     // 过温保护恢复阈值，根据NTC曲线设定，10K上拉电阻，70℃

/* 故障显示 */
#define LED_ONTime                          (100)
#define LED_OFFTime                         (233)
#define LED_ONOFFTime                       (LED_ONTime + LED_OFFTime)
#define LED_DelayTime                       (200)
#define LED_IntervalTime                    (1000)




typedef enum
{

    FaultNoSource           = 0,  ///< 无故障                                                  
    FaultHardOVCurrent      = 1,  ///< 硬件过流
    FaultSoftOVCurrent      = 2,  ///< 软件过流    
    FaultOverVoltageDC      = 3,  ///< 过压                                                 
    FaultUnderVoltageDC     = 4,  ///< 欠压
    FaultPhaseLost          = 5,  ///< 缺相
    FaultStall              = 6,  ///< 堵转                                                  
    FaultNtcOTErr           = 7,  ///< NTC过温
    FaultMotorOTErr         = 8,  ///< 电机过温
    FaultTSD                = 9,  ///< MCU内部过温
    FaultOTWarning          = 10, ///< IPM高温预警   
    FaultUartLost           = 11, ///< 通信丢失
    FaultPOST               = 12, ///< FCT自检故障
    FaultLVW 			    = 13, ///< LVW
    FaultGetOffset          = 14,       // 偏置电压保护 
    FaultOverPowerErr       = 15,  ///<功率保护
} FaultStateType;

typedef struct
{
    uint8 SWOC_DectTimeCnt;                                                           
                                                  
	int16 Is;               											
	
    uint8 HWOC_Times;
    uint8 HWOC_DectTimeCnt;
    
}FaultCurrentVarible;


typedef struct
{

   uint16 DetecCnt;
   

   uint16 WarningDetecCnt;

   uint8  WarningFlag; 
    
	
}FaultTemperatureVarible;


typedef struct
{  
    uint16 DectDealyCnt;
    uint16 DectCycleCnt;                                                          
    uint16 ALossCnt ;                                                           
    uint16 BLossCnt ;                                                           
    uint16 CLossCnt ;                                                           
    uint16 ABCLossCnt;															
    uint16 mcLossPHRecCount;                                                 
    
    
    uint16 Max_ia;
    uint16 Max_ib;
    uint16 Max_ic;
    
}FaultPhaseLossVarible;


typedef struct
{
    uint16 OverVoltDetecCnt;                                                     
    uint16 UnderVoltDetecCnt; 


    uint16 VoltRecoverCnt;                                                    
		
	  uint16 BusVoltDetecCnt;													     
	  
    uint16 DectDealyCnt;
	
	  uint16      VoltDetecBraketCount;
	  uint8       FlagBrakeInit;
	  uint16      VoltDetecBraketDuty;
    
}FaultVoltageVarible;




typedef struct
{
    uint16 EsValue;
    
    uint16 Mode0DectCnt;                                  
                                    
    int16  SpeedErr;															         																					
    uint16 DeviSpeedCnt;														       
	  
    uint16 EsDectCnt;                                 
    uint16 SpeedDectCnt;   
    uint16 SpeedMinCnt;   
  

    uint16 DectDealyCnt;
    uint8  Type;
  
}FaultStallTypedef;


typedef struct
{  
    uint16      OverPowerDetecCnt;        ///< 过功率检测次数
    
}FaultOverPower;


typedef struct
{
                          
    uint16 DC_DelayTcnt;
    uint16 LP_DelayTcnt;
    uint16 OT_DelayTcnt;
    uint16 SWOC_DelayTcnt;
    uint16 Stall_DealyTcnt;
    uint16 OverPower_DealyTcnt;
    
    uint8  OV_Times; 
    uint8  OT_Times;
    uint8  LP_Times;
    uint8  SWOC_Times;
    uint8  Stall_Times;
	
	uint8 OverPower_Times;
        
   
    
}FaultRecoverTypedef;



typedef struct
{
    FaultCurrentVarible         Current;
    FaultPhaseLossVarible       PhaseLoss;
    FaultVoltageVarible         Voltage;
    FaultStallTypedef           Stall;
    FaultTemperatureVarible     Temperature;
    FaultOverPower              Power;
}FaultVarible;


extern FaultStateType               data    mcFaultSource;
extern uint8                        xdata   mcPOSTErrSource;
extern FaultVarible                 xdata   fault;
extern FaultCurrentVarible          idata   mcCurVarible;
extern FaultRecoverTypedef          xdata   Restart;





/* Exported variables ---------------------------------------------------------------------------*/

/* Exported functions ---------------------------------------------------------------------------*/


extern void Fault_Detection(void);
extern void Fault_OverCurrent(void);
extern void Fault_Temperature(void);
extern void Fault_Voltage(void);
extern void Fault_Stall(void);
extern void Fault_PhaseLoss(void);
extern void UnderProcess(void);
extern void Fault_Power(void);
#endif

