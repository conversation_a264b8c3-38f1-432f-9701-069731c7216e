M:PIInit
F:G$PI_Init$0$0({2}DF,SV:S),Z,0,0,0,0,0
F:G$PI2_Init$0$0({2}DF,SV:S),Z,0,0,0,0,0
F:G$PI3_Init$0$0({2}DF,SV:S),Z,0,0,0,0,0
T:FPIInit$float_long[({0}S:S$f$0$0({4}SF:S),Z,0,0)({0}S:S$l$0$0({4}SL:S),Z,0,0)]
T:FPIInit$__00000000[({0}S:S$SWOC_DectTimeCnt$0$0({1}SC:U),Z,0,0)({1}S:S$Is$0$0({2}SI:S),Z,0,0)({3}S:S$HWOC_Times$0$0({1}SC:U),Z,0,0)({4}S:S$HWOC_DectTimeCnt$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000010[({0}S:S$Times$0$0({4}SL:U),Z,0,0)({4}S:S$TimeCnt$0$0({2}SI:U),Z,0,0)({6}S:S$State$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000001[({0}S:S$DetecCnt$0$0({2}SI:U),Z,0,0)({2}S:S$WarningDetecCnt$0$0({2}SI:U),Z,0,0)({4}S:S$WarningFlag$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000020[({0}S:S$BEMFSpeed$0$0({2}SI:S),Z,0,0)({2}S:S$BEMFSpeedBase$0$0({4}SL:U),Z,0,0)({6}S:S$Status$0$0({1}SC:U),Z,0,0)({7}S:S$FR_SET$0$0({1}SC:U),Z,0,0)({8}S:S$FR$0$0({1}SC:U),Z,0,0)({9}S:S$FRPre$0$0({1}SC:U),Z,0,0)({10}S:S$FRCount$0$0({1}SC:S),Z,0,0)({11}S:S$SpeedUpdate$0$0({1}SC:U),Z,0,0)({12}S:S$HighSpdStart$0$0({1}SC:U),Z,0,0)({13}S:S$PeriodTime$0$0({2}SI:U),Z,0,0)({15}S:S$SectorTime$0$0({12}DA6d,SI:U),Z,0,0)]
T:FPIInit$__00000011[({0}S:S$LedCount$0$0({2}SI:U),Z,0,0)({2}S:S$Counttime$0$0({2}SI:U),Z,0,0)({4}S:S$Count$0$0({2}SI:U),Z,0,0)({6}S:S$LedTimCot$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000002[({0}S:S$DectDealyCnt$0$0({2}SI:U),Z,0,0)({2}S:S$DectCycleCnt$0$0({2}SI:U),Z,0,0)({4}S:S$ALossCnt$0$0({2}SI:U),Z,0,0)({6}S:S$BLossCnt$0$0({2}SI:U),Z,0,0)({8}S:S$CLossCnt$0$0({2}SI:U),Z,0,0)({10}S:S$ABCLossCnt$0$0({2}SI:U),Z,0,0)({12}S:S$mcLossPHRecCount$0$0({2}SI:U),Z,0,0)({14}S:S$Max_ia$0$0({2}SI:U),Z,0,0)({16}S:S$Max_ib$0$0({2}SI:U),Z,0,0)({18}S:S$Max_ic$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000021[({0}S:S$s32$0$0({4}SL:S),Z,0,0)({0}S:S$s16$0$0({4}DA2d,SI:S),Z,0,0)]
T:FPIInit$__00000012[({0}S:S$mcDcbusFlt$0$0({2}SI:U),Z,0,0)({2}S:S$CtrlMode$0$0({1}SC:U),Z,0,0)({3}S:S$mcADCCurrentbus$0$0({2}SI:U),Z,0,0)({5}S:S$Power$0$0({2}SI:S),Z,0,0)({7}S:S$PowerFlt$0$0({2}SI:S),Z,0,0)({9}S:S$SpeedFlt$0$0({2}SI:S),Z,0,0)({11}S:S$UqFlt$0$0({2}SI:S),Z,0,0)({13}S:S$UdFlt$0$0({2}SI:S),Z,0,0)({15}S:S$ChargeStep$0$0({1}SC:U),Z,0,0)({16}S:S$Start_Mode$0$0({1}SC:U),Z,0,0)({17}S:S$Max_ia$0$0({2}SI:S),Z,0,0)({19}S:S$Max_ib$0$0({2}SI:S),Z,0,0)({21}S:S$Max_ic$0$0({2}SI:S),Z,0,0)({23}S:S$FR_SET$0$0({1}SC:U),Z,0,0)({24}S:S$FR$0$0({1}SC:U),Z,0,0)({25}S:S$Flg_ATORampEnd$0$0({1}SC:U),Z,0,0)({26}S:S$Ref$0$0({2}SI:S),Z,0,0)({28}S:S$IqRef$0$0({2}SI:S),Z,0,0)({30}S:S$IdRef$0$0({2}SI:S),Z,0,0)({32}S:S$IqSpeedRef$0$0({2}SI:S),Z,0,0)({34}S:S$IqADCCurrentRef$0$0({2}SI:S),Z,0,0)({36}S:S$ExtDec$0$0({2}SI:S),Z,0,0)({38}S:S$LimitIqOut$0$0({2}SI:S),Z,0,0)({40}S:S$PowerLimitValue$0$0({2}SI:U),Z,0,0)({42}S:S$Mode0HoldCnt$0$0({1}SC:U),Z,0,0)({43}S:S$EMFsquare$0$0({2}SI:U),Z,0,0)({45}S:S$RunStateCnt$0$0({2}SI:U),Z,0,0)({47}S:S$LoopTime$0$0({1}SC:U),Z,0,0)({48}S:S$State_Count$0$0({2}SI:U),Z,0,0)({50}S:S$mcPosCheckAngle$0$0({2}SI:S),Z,0,0)({52}S:S$MCU_TEMP$0$0({1}SC:U),Z,0,0)({53}S:S$Angle$0$0({2}SI:S),Z,0,0)({55}S:S$sqrtUdq$0$0({2}SI:U),Z,0,0)({57}S:S$Refcnt$0$0({2}SI:U),Z,0,0)({59}S:S$ZeroFlag$0$0({1}SC:U),Z,0,0)({60}S:S$ZeroLossFlag$0$0({1}SC:U),Z,0,0)({61}S:S$ZeroCnt$0$0({2}SI:U),Z,0,0)({63}S:S$ZeroLossCnt$0$0({2}SI:U),Z,0,0)({65}S:S$NTCValue$0$0({2}SI:S),Z,0,0)({67}S:S$NTCValueFlt$0$0({2}SI:S),Z,0,0)({69}S:S$NTCValueGatherFlag$0$0({1}SC:U),Z,0,0)({70}S:S$NTCValueGatherCnt$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000003[({0}S:S$OverVoltDetecCnt$0$0({2}SI:U),Z,0,0)({2}S:S$UnderVoltDetecCnt$0$0({2}SI:U),Z,0,0)({4}S:S$VoltRecoverCnt$0$0({2}SI:U),Z,0,0)({6}S:S$BusVoltDetecCnt$0$0({2}SI:U),Z,0,0)({8}S:S$DectDealyCnt$0$0({2}SI:U),Z,0,0)({10}S:S$VoltDetecBraketCount$0$0({2}SI:U),Z,0,0)({12}S:S$FlagBrakeInit$0$0({1}SC:U),Z,0,0)({13}S:S$VoltDetecBraketDuty$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000022[({0}S:S$mcDcbusFlt1$0$0({2}SI:U),Z,0,0)({2}S:S$AverageVoltageValue$0$0({2}SI:U),Z,0,0)({4}S:S$AverageVoltageValue1$0$0({2}SI:U),Z,0,0)({6}S:S$mcDcbusFlt2Sum$0$0({4}ST__00000021:S),Z,0,0)({10}S:S$UQVALUESum$0$0({4}ST__00000021:S),Z,0,0)({14}S:S$UDVALUESum$0$0({4}ST__00000021:S),Z,0,0)({18}S:S$UQVALUEAVERAGE$0$0({2}SI:S),Z,0,0)({20}S:S$UDVALUEAVERAGE$0$0({2}SI:S),Z,0,0)({22}S:S$mcDcbusFlt2cnt$0$0({2}SI:U),Z,0,0)({24}S:S$cpscnt$0$0({2}SI:U),Z,0,0)({26}S:S$Uqcps$0$0({2}SI:U),Z,0,0)({28}S:S$Udcps$0$0({2}SI:U),Z,0,0)({30}S:S$testUq$0$0({2}SI:U),Z,0,0)({32}S:S$testUq1$0$0({2}SI:U),Z,0,0)({34}S:S$testUq2$0$0({2}SI:U),Z,0,0)({36}S:S$testUq3$0$0({2}SI:U),Z,0,0)({38}S:S$testUq4$0$0({2}SI:S),Z,0,0)({40}S:S$testUd$0$0({2}SI:S),Z,0,0)({42}S:S$testUd2$0$0({2}SI:U),Z,0,0)({44}S:S$testUd3$0$0({2}SI:S),Z,0,0)({46}S:S$testUd4$0$0({2}SI:U),Z,0,0)({48}S:S$segmentationcnt$0$0({1}SC:U),Z,0,0)({49}S:S$SPIVar1$0$0({2}SI:S),Z,0,0)({51}S:S$SPIVar2$0$0({2}SI:S),Z,0,0)({53}S:S$SPIVar3$0$0({2}SI:S),Z,0,0)({55}S:S$Undervoltage_flag$0$0({1}SC:U),Z,0,0)({56}S:S$IncVoltage$0$0({2}SI:U),Z,0,0)({58}S:S$LineAngel$0$0({2}SI:S),Z,0,0)({60}S:S$LineAngelMax$0$0({2}SI:S),Z,0,0)({62}S:S$LineAngelMin$0$0({2}SI:S),Z,0,0)({64}S:S$VCDelayCnt$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000013[({0}S:S$RefValue$0$0({2}SI:S),Z,0,0)({2}S:S$OutValue_float$0$0({4}SF:S),Z,0,0)({6}S:S$IncValue$0$0({4}SF:S),Z,0,0)({10}S:S$DecValue$0$0({4}SF:S),Z,0,0)]
T:FPIInit$__00000004[({0}S:S$EsValue$0$0({2}SI:U),Z,0,0)({2}S:S$Mode0DectCnt$0$0({2}SI:U),Z,0,0)({4}S:S$SpeedErr$0$0({2}SI:S),Z,0,0)({6}S:S$DeviSpeedCnt$0$0({2}SI:U),Z,0,0)({8}S:S$EsDectCnt$0$0({2}SI:U),Z,0,0)({10}S:S$SpeedDectCnt$0$0({2}SI:U),Z,0,0)({12}S:S$SpeedMinCnt$0$0({2}SI:U),Z,0,0)({14}S:S$DectDealyCnt$0$0({2}SI:U),Z,0,0)({16}S:S$Type$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000023[({0}S:S$LEDCnt$0$0({2}SI:U),Z,0,0)({2}S:S$FlagLED$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000014[({0}S:S$Compare$0$0({2}SI:U),Z,0,0)({2}S:S$Period$0$0({2}SI:U),Z,0,0)({4}S:S$TimerDR$0$0({2}SI:U),Z,0,0)({6}S:S$TimerDROld$0$0({2}SI:U),Z,0,0)({8}S:S$TimerARR$0$0({2}SI:U),Z,0,0)({10}S:S$TimerARROld$0$0({2}SI:U),Z,0,0)({12}S:S$Duty$0$0({2}SI:U),Z,0,0)({14}S:S$Freq$0$0({2}SI:U),Z,0,0)({16}S:S$isUpdate$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000005[({0}S:S$OverPowerDetecCnt$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000024[({0}S:S$Key1Value$0$0({1}SC:U),Z,0,0)({1}S:S$Key2Value$0$0({1}SC:U),Z,0,0)({2}S:S$Key3Value$0$0({1}SC:U),Z,0,0)({3}S:S$Key1PressCnt$0$0({2}SI:U),Z,0,0)({5}S:S$Key2PressCnt$0$0({2}SI:U),Z,0,0)({7}S:S$Key2PressCnt2$0$0({2}SI:U),Z,0,0)({9}S:S$Key3PressCnt$0$0({2}SI:U),Z,0,0)({11}S:S$Key3releaseCnt$0$0({2}SI:U),Z,0,0)({13}S:S$KeyADCValue$0$0({2}SI:U),Z,0,0)({15}S:S$KeyValuetotal$0$0({1}SC:U),Z,0,0)({16}S:S$OldKeyValuetotal$0$0({1}SC:U),Z,0,0)({17}S:S$ChangeKeyFlg$0$0({1}SC:U),Z,0,0)({18}S:S$TwoCarrierCnt$0$0({1}SC:U),Z,0,0)({19}S:S$ColdHotFlag$0$0({1}SC:U),Z,0,0)({20}S:S$ONOFFCNT$0$0({2}SI:U),Z,0,0)({22}S:S$ONOFFFlag$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000015[({0}S:S$SetMode$0$0({1}SC:U),Z,0,0)({0}S:S$SetFlag$0$0({1}ST__00000016:S),Z,0,0)]
T:FPIInit$__00000006[({0}S:S$DC_DelayTcnt$0$0({2}SI:U),Z,0,0)({2}S:S$LP_DelayTcnt$0$0({2}SI:U),Z,0,0)({4}S:S$OT_DelayTcnt$0$0({2}SI:U),Z,0,0)({6}S:S$SWOC_DelayTcnt$0$0({2}SI:U),Z,0,0)({8}S:S$Stall_DealyTcnt$0$0({2}SI:U),Z,0,0)({10}S:S$OverPower_DealyTcnt$0$0({2}SI:U),Z,0,0)({12}S:S$OV_Times$0$0({1}SC:U),Z,0,0)({13}S:S$OT_Times$0$0({1}SC:U),Z,0,0)({14}S:S$LP_Times$0$0({1}SC:U),Z,0,0)({15}S:S$SWOC_Times$0$0({1}SC:U),Z,0,0)({16}S:S$Stall_Times$0$0({1}SC:U),Z,0,0)({17}S:S$OverPower_Times$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000025[({0}S:S$ScrOnAngleTimeDatum$0$0({1}SC:U),Z,0,0)({1}S:S$ScrOnAngleTime$0$0({1}SC:U),Z,0,0)({2}S:S$ScrOnPeriod$0$0({1}SC:U),Z,0,0)({3}S:S$Temperature$0$0({2}SI:U),Z,0,0)({5}S:S$TemperatureDatum$0$0({6}DA3d,SI:U),Z,0,0)({11}S:S$TPCtrlDealy$0$0({2}SI:U),Z,0,0)({13}S:S$ScrOnZeroFlag$0$0({1}SC:U),Z,0,0)({14}S:S$ScrOnZeroCnt$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000016[({0}S:S$CalibFlag$0$0({1}SB0$1:U),Z,0,0)({0}S:S$ChargeSetFlag$0$0({1}SB1$1:U),Z,0,0)({0}S:S$AlignSetFlag$0$0({1}SB2$1:U),Z,0,0)({0}S:S$TailWindSetFlag$0$0({1}SB3$1:U),Z,0,0)({0}S:S$StartSetFlag$0$0({1}SB4$1:U),Z,0,0)({0}S:S$PosiCheckSetFlag$0$0({1}SB5$1:U),Z,0,0)({0}S:S$Break$0$0({1}SB6$1:U),Z,0,0)]
T:FPIInit$__00000007[({0}S:S$Current$0$0({5}ST__00000000:S),Z,0,0)({5}S:S$PhaseLoss$0$0({20}ST__00000002:S),Z,0,0)({25}S:S$Voltage$0$0({15}ST__00000003:S),Z,0,0)({40}S:S$Stall$0$0({17}ST__00000004:S),Z,0,0)({57}S:S$Temperature$0$0({5}ST__00000001:S),Z,0,0)({62}S:S$Power$0$0({2}ST__00000005:S),Z,0,0)]
T:FPIInit$__00000026[({0}S:S$WeakenRunUq$0$0({2}SI:S),Z,0,0)({2}S:S$WeakenRunUd$0$0({2}SI:S),Z,0,0)({4}S:S$WeakenUsRef$0$0({2}SI:U),Z,0,0)({6}S:S$WeakenUsFed$0$0({2}SI:U),Z,0,0)({8}S:S$ThetaOutmin$0$0({2}SI:S),Z,0,0)({10}S:S$ThetaOutmax$0$0({2}SI:S),Z,0,0)({12}S:S$WeakenTheta$0$0({2}SI:S),Z,0,0)({14}S:S$udFlt$0$0({2}SI:S),Z,0,0)({16}S:S$uqFlt$0$0({2}SI:S),Z,0,0)({18}S:S$mcIsref$0$0({2}SI:S),Z,0,0)({20}S:S$IdRef$0$0({2}SI:S),Z,0,0)({22}S:S$IqRef$0$0({2}SI:S),Z,0,0)]
T:FPIInit$__00000017[({0}S:S$StepTime$0$0({8}DA4d,SI:U),Z,0,0)({8}S:S$Times$0$0({1}SC:U),Z,0,0)({9}S:S$Period$0$0({2}SI:U),Z,0,0)({11}S:S$Count$0$0({2}SI:S),Z,0,0)({13}S:S$CountPre$0$0({2}SI:S),Z,0,0)({15}S:S$State$0$0({2}SI:S),Z,0,0)({17}S:S$Speed$0$0({2}SI:S),Z,0,0)({19}S:S$SpeedUpdate$0$0({1}SC:U),Z,0,0)({20}S:S$HighSpdStart$0$0({1}SC:U),Z,0,0)({21}S:S$RSDSpeedBase$0$0({4}SL:U),Z,0,0)({25}S:S$RSDCCWSBRCnt$0$0({2}SI:U),Z,0,0)({27}S:S$RSDStep$0$0({1}SC:U),Z,0,0)({28}S:S$SetFR$0$0({1}SC:U),Z,0,0)({29}S:S$Status$0$0({1}SC:U),Z,0,0)({30}S:S$ArrCnt$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000008[({0}S:S$CheckCount$0$0({1}SC:U),Z,0,0)({1}S:S$TSD_Enable_Flag$0$0({1}SC:U),Z,0,0)({2}S:S$TSD_Trigger_Flag$0$0({1}SC:U),Z,0,0)({3}S:S$ActualTemper$0$0({1}SC:U),Z,0,0)({4}S:S$Trigger_Value$0$0({2}SI:U),Z,0,0)({6}S:S$TSD_Dec_Time$0$0({2}SI:U),Z,0,0)]
T:FPIInit$__00000018[({0}S:S$InsetIdStep1$0$0({12}DA6d,SI:U),Z,0,0)({12}S:S$InsetIdStep2$0$0({6}DA3d,SI:U),Z,0,0)({18}S:S$InsetIdStep3$0$0({4}DA2d,SI:U),Z,0,0)({22}S:S$InsetIdMax$0$0({2}SI:U),Z,0,0)({24}S:S$injectmax1$0$0({1}SC:U),Z,0,0)({25}S:S$injectmax2$0$0({1}SC:U),Z,0,0)({26}S:S$injectmax3$0$0({1}SC:U),Z,0,0)({27}S:S$Angle$0$0({2}SI:S),Z,0,0)({29}S:S$ThetaGet$0$0({2}SI:S),Z,0,0)({31}S:S$injecttimes$0$0({1}SC:U),Z,0,0)({32}S:S$injectstep$0$0({1}SC:U),Z,0,0)({33}S:S$injectCnt$0$0({1}SC:U),Z,0,0)({34}S:S$injectCntTemp$0$0({1}SC:U),Z,0,0)({35}S:S$InjectOffFocIntCnt$0$0({2}SI:U),Z,0,0)({37}S:S$InjectOnFocIntCnt$0$0({2}SI:U),Z,0,0)({39}S:S$InjectStep1VoltageProportion$0$0({2}SI:U),Z,0,0)({41}S:S$InjectStep2VoltageProportion$0$0({2}SI:U),Z,0,0)({43}S:S$Theta$0$0({2}SI:S),Z,0,0)({45}S:S$ForwardDirectionVectorFlag$0$0({1}SC:U),Z,0,0)({46}S:S$OppositeDirectionVectorFlag$0$0({1}SC:U),Z,0,0)({47}S:S$ZeroVectorFlag$0$0({1}SC:U),Z,0,0)({48}S:S$NextShotFlag$0$0({1}SC:U),Z,0,0)({49}S:S$injectstartflag$0$0({1}SC:U),Z,0,0)({50}S:S$injectcntstartflag$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000009[({0}S:S$WriteAddress$0$0({2}SI:U),Z,0,0)({2}S:S$WriteAddress_Top$0$0({2}SI:U),Z,0,0)({4}S:S$WriteValue$0$0({8}DA8d,SC:U),Z,0,0)({12}S:S$ReadAddress$0$0({2}SI:U),Z,0,0)({14}S:S$ReadValue$0$0({8}DA8d,SC:U),Z,0,0)({22}S:S$FlagFlashWrite$0$0({1}SC:U),Z,0,0)({23}S:S$FlagFlashWrite_Start$0$0({1}SC:U),Z,0,0)({24}S:S$Flag_FlashErase$0$0({1}SC:U),Z,0,0)]
T:FPIInit$__00000019[({0}S:S$IuOffset$0$0({2}SI:S),Z,0,0)({2}S:S$IuOffsetSum$0$0({4}SL:S),Z,0,0)({6}S:S$IvOffset$0$0({2}SI:S),Z,0,0)({8}S:S$IvOffsetSum$0$0({4}SL:S),Z,0,0)({12}S:S$Iw_busOffset$0$0({2}SI:S),Z,0,0)({14}S:S$Iw_busOffsetSum$0$0({4}SL:S),Z,0,0)({18}S:S$OffsetCount$0$0({2}SI:S),Z,0,0)({20}S:S$OffsetFlag$0$0({1}SC:S),Z,0,0)]
S:G$mcFaultSource$0$0({1}SC:U),E,0,0
S:G$mcRefRamp$0$0({14}ST__00000013:S),E,0,0
S:G$mcState$0$0({1}SC:U),E,0,0
S:G$VoltageComp$0$0({66}ST__00000022:S),E,0,0
S:G$mcCurVarible$0$0({5}ST__00000000:S),G,0,0
S:G$isCtrlPowOn$0$0({1}SB0$1:U),H,0,0
S:LPIInit.islower$c$1$13({2}SI:S),F,0,0
S:LPIInit.isupper$c$1$15({2}SI:S),F,0,0
S:LPIInit.isblank$c$1$17({2}SI:S),F,0,0
S:LPIInit.isdigit$c$1$19({2}SI:S),F,0,0
S:G$mcPOSTErrSource$0$0({1}SC:U),F,0,0
S:G$fault$0$0({64}ST__00000007:S),F,0,0
S:G$Restart$0$0({18}ST__00000006:S),F,0,0
S:G$spidebug$0$0({8}DA4d,SI:U),F,0,0
S:G$TSDTemperature$0$0({8}ST__00000008:S),F,0,0
S:G$FlashData$0$0({25}ST__00000009:S),F,0,0
S:G$debug_ONOFFTest$0$0({7}ST__00000010:S),F,0,0
S:G$mcFocCtrl$0$0({72}ST__00000012:S),F,0,0
S:G$mcPwmInput$0$0({17}ST__00000014:S),F,0,0
S:G$McStaSet$0$0({1}ST__00000015:S),F,0,0
S:G$mcRsd$0$0({31}ST__00000017:S),F,0,0
S:G$RPDPara$0$0({51}ST__00000018:S),F,0,0
S:G$RPD_Status$0$0({1}SC:U),F,0,0
S:G$mcCurOffset$0$0({21}ST__00000019:S),F,0,0
S:G$Power_Currt$0$0({2}SI:U),F,0,0
S:G$mcBemf$0$0({27}ST__00000020:S),F,0,0
S:G$LEDCtl$0$0({3}ST__00000023:S),F,0,0
S:G$KS$0$0({23}ST__00000024:S),F,0,0
S:G$User$0$0({16}ST__00000025:S),F,0,0
S:G$mcFiledWeaken$0$0({24}ST__00000026:S),F,0,0
S:G$DPTR$0$0({2}SI:U),I,0,0
S:G$PSW$0$0({1}SC:U),I,0,0
S:G$ACC$0$0({1}SC:U),I,0,0
S:G$RST_SR$0$0({1}SC:U),I,0,0
S:G$TCON$0$0({1}SC:U),I,0,0
S:G$IE$0$0({1}SC:U),I,0,0
S:G$IP0$0$0({1}SC:U),I,0,0
S:G$IP1$0$0({1}SC:U),I,0,0
S:G$IP2$0$0({1}SC:U),I,0,0
S:G$IP3$0$0({1}SC:U),I,0,0
S:G$UT_CR$0$0({1}SC:U),I,0,0
S:G$UT_DR$0$0({1}SC:U),I,0,0
S:G$UT_BAUD$0$0({2}SI:U),I,0,0
S:G$UT2_CR$0$0({1}SC:U),I,0,0
S:G$UT2_DR$0$0({1}SC:U),I,0,0
S:G$MDU_CR$0$0({1}SC:U),I,0,0
S:G$PI_CR$0$0({1}SC:U),I,0,0
S:G$TIM2_CR0$0$0({1}SC:U),I,0,0
S:G$TIM2_CR1$0$0({1}SC:U),I,0,0
S:G$TIM2__DR$0$0({2}SI:U),I,0,0
S:G$TIM2__ARR$0$0({2}SI:U),I,0,0
S:G$TIM2__CNTR$0$0({2}SI:U),I,0,0
S:G$TIM3_CR0$0$0({1}SC:U),I,0,0
S:G$TIM3_CR1$0$0({1}SC:U),I,0,0
S:G$TIM3__DR$0$0({2}SI:U),I,0,0
S:G$TIM3__ARR$0$0({2}SI:U),I,0,0
S:G$TIM3__CNTR$0$0({2}SI:U),I,0,0
S:G$TIM4_CR0$0$0({1}SC:U),I,0,0
S:G$TIM4_CR1$0$0({1}SC:U),I,0,0
S:G$TIM4__DR$0$0({2}SI:U),I,0,0
S:G$TIM4__ARR$0$0({2}SI:U),I,0,0
S:G$TIM4__CNTR$0$0({2}SI:U),I,0,0
S:G$DRV_OUT$0$0({1}SC:U),I,0,0
S:G$P0$0$0({1}SC:U),I,0,0
S:G$P1$0$0({1}SC:U),I,0,0
S:G$P2$0$0({1}SC:U),I,0,0
S:G$P3$0$0({1}SC:U),I,0,0
S:G$P4$0$0({1}SC:U),I,0,0
S:G$P5$0$0({1}SC:U),I,0,0
S:G$P0_OE$0$0({1}SC:U),I,0,0
S:G$P1_IE$0$0({1}SC:U),I,0,0
S:G$P1_IF$0$0({1}SC:U),I,0,0
S:G$P1_OE$0$0({1}SC:U),I,0,0
S:G$P2_OE$0$0({1}SC:U),I,0,0
S:G$P3_OE$0$0({1}SC:U),I,0,0
S:G$P4_IE$0$0({1}SC:U),I,0,0
S:G$P4_IF$0$0({1}SC:U),I,0,0
S:G$P4_OE$0$0({1}SC:U),I,0,0
S:G$P5_OE$0$0({1}SC:U),I,0,0
S:G$CMP_CR0$0$0({1}SC:U),I,0,0
S:G$CMP_CR1$0$0({1}SC:U),I,0,0
S:G$CMP_CR2$0$0({1}SC:U),I,0,0
S:G$CMP_CR3$0$0({1}SC:U),I,0,0
S:G$CMP_CR4$0$0({1}SC:U),I,0,0
S:G$HALL_CR$0$0({1}SC:U),I,0,0
S:G$CMP_SR$0$0({1}SC:U),I,0,0
S:G$EVT_FILT$0$0({1}SC:U),I,0,0
S:G$FLA_KEY$0$0({1}SC:U),I,0,0
S:G$FLA_CR$0$0({1}SC:U),I,0,0
S:G$PCON$0$0({1}SC:U),I,0,0
S:G$LVSR$0$0({1}SC:U),I,0,0
S:G$CY$0$0({1}SX:U),J,0,0
S:G$AC$0$0({1}SX:U),J,0,0
S:G$F0$0$0({1}SX:U),J,0,0
S:G$RS1$0$0({1}SX:U),J,0,0
S:G$RS0$0$0({1}SX:U),J,0,0
S:G$OV$0$0({1}SX:U),J,0,0
S:G$F1$0$0({1}SX:U),J,0,0
S:G$P$0$0({1}SX:U),J,0,0
S:G$TSDIF$0$0({1}SX:U),J,0,0
S:G$IT11$0$0({1}SX:U),J,0,0
S:G$IT10$0$0({1}SX:U),J,0,0
S:G$IF0$0$0({1}SX:U),J,0,0
S:G$IT01$0$0({1}SX:U),J,0,0
S:G$IT00$0$0({1}SX:U),J,0,0
S:G$EA$0$0({1}SX:U),J,0,0
S:G$RTCIE$0$0({1}SX:U),J,0,0
S:G$ES0$0$0({1}SX:U),J,0,0
S:G$SPIIE$0$0({1}SX:U),J,0,0
S:G$EX1$0$0({1}SX:U),J,0,0
S:G$TSDIE$0$0({1}SX:U),J,0,0
S:G$EX0$0$0({1}SX:U),J,0,0
S:G$UT_MOD1$0$0({1}SX:U),J,0,0
S:G$UT_MOD0$0$0({1}SX:U),J,0,0
S:G$SM2$0$0({1}SX:U),J,0,0
S:G$REN$0$0({1}SX:U),J,0,0
S:G$TB8$0$0({1}SX:U),J,0,0
S:G$RB8$0$0({1}SX:U),J,0,0
S:G$TI$0$0({1}SX:U),J,0,0
S:G$RI$0$0({1}SX:U),J,0,0
S:G$UT2MOD1$0$0({1}SX:U),J,0,0
S:G$UT2MOD0$0$0({1}SX:U),J,0,0
S:G$UT2SM2$0$0({1}SX:U),J,0,0
S:G$UT2REN$0$0({1}SX:U),J,0,0
S:G$UT2TB8$0$0({1}SX:U),J,0,0
S:G$UT2RB8$0$0({1}SX:U),J,0,0
S:G$UT2TI$0$0({1}SX:U),J,0,0
S:G$UT2RI$0$0({1}SX:U),J,0,0
S:G$MOE$0$0({1}SX:U),J,0,0
S:G$OISWL$0$0({1}SX:U),J,0,0
S:G$OISWH$0$0({1}SX:U),J,0,0
S:G$OISVL$0$0({1}SX:U),J,0,0
S:G$OISVH$0$0({1}SX:U),J,0,0
S:G$OISUL$0$0({1}SX:U),J,0,0
S:G$OISUH$0$0({1}SX:U),J,0,0
S:G$GP00$0$0({1}SX:U),J,0,0
S:G$GP01$0$0({1}SX:U),J,0,0
S:G$GP02$0$0({1}SX:U),J,0,0
S:G$GP03$0$0({1}SX:U),J,0,0
S:G$GP04$0$0({1}SX:U),J,0,0
S:G$GP05$0$0({1}SX:U),J,0,0
S:G$GP06$0$0({1}SX:U),J,0,0
S:G$GP07$0$0({1}SX:U),J,0,0
S:G$GP10$0$0({1}SX:U),J,0,0
S:G$GP11$0$0({1}SX:U),J,0,0
S:G$GP12$0$0({1}SX:U),J,0,0
S:G$GP13$0$0({1}SX:U),J,0,0
S:G$GP14$0$0({1}SX:U),J,0,0
S:G$GP15$0$0({1}SX:U),J,0,0
S:G$GP16$0$0({1}SX:U),J,0,0
S:G$GP17$0$0({1}SX:U),J,0,0
S:G$GP20$0$0({1}SX:U),J,0,0
S:G$GP21$0$0({1}SX:U),J,0,0
S:G$GP22$0$0({1}SX:U),J,0,0
S:G$GP23$0$0({1}SX:U),J,0,0
S:G$GP24$0$0({1}SX:U),J,0,0
S:G$GP25$0$0({1}SX:U),J,0,0
S:G$GP26$0$0({1}SX:U),J,0,0
S:G$GP27$0$0({1}SX:U),J,0,0
S:G$GP30$0$0({1}SX:U),J,0,0
S:G$GP31$0$0({1}SX:U),J,0,0
S:G$GP32$0$0({1}SX:U),J,0,0
S:G$GP33$0$0({1}SX:U),J,0,0
S:G$GP34$0$0({1}SX:U),J,0,0
S:G$GP35$0$0({1}SX:U),J,0,0
S:G$GP36$0$0({1}SX:U),J,0,0
S:G$GP37$0$0({1}SX:U),J,0,0
S:G$GP40$0$0({1}SX:U),J,0,0
S:G$GP41$0$0({1}SX:U),J,0,0
S:G$GP42$0$0({1}SX:U),J,0,0
S:G$GP43$0$0({1}SX:U),J,0,0
S:G$GP44$0$0({1}SX:U),J,0,0
S:G$GP45$0$0({1}SX:U),J,0,0
S:G$GP46$0$0({1}SX:U),J,0,0
S:G$GP47$0$0({1}SX:U),J,0,0
S:G$GP50$0$0({1}SX:U),J,0,0
S:G$GP51$0$0({1}SX:U),J,0,0
S:G$GP52$0$0({1}SX:U),J,0,0
S:G$GP53$0$0({1}SX:U),J,0,0
S:G$isprint$0$0({2}DF,SI:S),C,0,0
S:G$ispunct$0$0({2}DF,SI:S),C,0,0
S:G$isspace$0$0({2}DF,SI:S),C,0,0
S:G$isxdigit$0$0({2}DF,SI:S),C,0,0
S:G$isalnum$0$0({2}DF,SI:S),C,0,0
S:G$isalpha$0$0({2}DF,SI:S),C,0,0
S:G$tolower$0$0({2}DF,SI:S),C,0,0
S:G$toupper$0$0({2}DF,SI:S),C,0,0
S:G$iscntrl$0$0({2}DF,SI:S),C,0,0
S:G$isgraph$0$0({2}DF,SI:S),C,0,0
S:G$islower$0$0({2}DF,SI:S),C,0,0
S:G$isupper$0$0({2}DF,SI:S),C,0,0
S:G$isblank$0$0({2}DF,SI:S),C,0,0
S:G$isdigit$0$0({2}DF,SI:S),C,0,0
S:G$_cror_$0$0({2}DF,SC:U),C,0,0
S:G$_iror_$0$0({2}DF,SI:U),C,0,0
S:G$_lror_$0$0({2}DF,SL:U),C,0,0
S:G$_crol_$0$0({2}DF,SC:U),C,0,0
S:G$_irol_$0$0({2}DF,SI:U),C,0,0
S:G$_lrol_$0$0({2}DF,SL:U),C,0,0
S:G$_chkfloat_$0$0({2}DF,SC:U),C,0,0
S:G$sinf$0$0({2}DF,SF:S),C,0,0
S:G$cosf$0$0({2}DF,SF:S),C,0,0
S:G$tanf$0$0({2}DF,SF:S),C,0,0
S:G$cotf$0$0({2}DF,SF:S),C,0,0
S:G$asinf$0$0({2}DF,SF:S),C,0,0
S:G$acosf$0$0({2}DF,SF:S),C,0,0
S:G$atanf$0$0({2}DF,SF:S),C,0,0
S:G$atan2f$0$0({2}DF,SF:S),C,0,0
S:G$sinhf$0$0({2}DF,SF:S),C,0,0
S:G$coshf$0$0({2}DF,SF:S),C,0,0
S:G$tanhf$0$0({2}DF,SF:S),C,0,0
S:G$expf$0$0({2}DF,SF:S),C,0,0
S:G$logf$0$0({2}DF,SF:S),C,0,0
S:G$log10f$0$0({2}DF,SF:S),C,0,0
S:G$powf$0$0({2}DF,SF:S),C,0,0
S:G$sqrtf$0$0({2}DF,SF:S),C,0,0
S:G$fabsf$0$0({2}DF,SF:S),C,0,0
S:G$frexpf$0$0({2}DF,SF:S),C,0,0
S:G$ldexpf$0$0({2}DF,SF:S),C,0,0
S:G$ceilf$0$0({2}DF,SF:S),C,0,0
S:G$floorf$0$0({2}DF,SF:S),C,0,0
S:G$modff$0$0({2}DF,SF:S),C,0,0
S:G$isnan$0$0({2}DF,SI:S),C,0,0
S:G$isinf$0$0({2}DF,SI:S),C,0,0
S:G$__setjmp$0$0({2}DF,SI:S),C,0,0
S:G$longjmp$0$0({2}DF,SV:S),C,0,0
S:G$_print_format$0$0({2}DF,SI:S),C,0,0
S:G$printf$0$0({2}DF,SI:S),C,0,0
S:G$vprintf$0$0({2}DF,SI:S),C,0,0
S:G$sprintf$0$0({2}DF,SI:S),C,0,0
S:G$vsprintf$0$0({2}DF,SI:S),C,0,0
S:G$puts$0$0({2}DF,SI:S),C,0,0
S:G$printf_small$0$0({2}DF,SV:S),C,0,0
S:G$getchar$0$0({2}DF,SC:S),C,0,0
S:G$putchar$0$0({2}DF,SC:S),C,0,0
S:G$gets$0$0({2}DF,DG,SC:S),C,0,0
S:G$atof$0$0({2}DF,SF:S),C,0,0
S:G$atoi$0$0({2}DF,SI:S),C,0,0
S:G$atol$0$0({2}DF,SL:S),C,0,0
S:G$atoll$0$0({2}DF,SI:S),C,0,0
S:G$strtol$0$0({2}DF,SL:S),C,0,0
S:G$strtoul$0$0({2}DF,SL:U),C,0,0
S:G$_uitoa$0$0({2}DF,SV:S),C,0,0
S:G$_itoa$0$0({2}DF,SV:S),C,0,0
S:G$_ultoa$0$0({2}DF,SV:S),C,0,0
S:G$_ltoa$0$0({2}DF,SV:S),C,0,0
S:G$rand$0$0({2}DF,SI:S),C,0,0
S:G$srand$0$0({2}DF,SV:S),C,0,0
S:G$calloc$0$0({2}DF,DX,SV:S),C,0,0
S:G$malloc$0$0({2}DF,DX,SV:S),C,0,0
S:G$realloc$0$0({2}DF,DX,SV:S),C,0,0
S:G$free$0$0({2}DF,SV:S),C,0,0
S:G$bsearch$0$0({2}DF,DG,SV:S),C,0,0
S:G$qsort$0$0({2}DF,SV:S),C,0,0
S:G$abs$0$0({2}DF,SI:S),C,0,0
S:G$labs$0$0({2}DF,SL:S),C,0,0
S:G$mblen$0$0({2}DF,SI:S),C,0,0
S:G$mbtowc$0$0({2}DF,SI:S),C,0,0
S:G$wctomb$0$0({2}DF,SI:S),C,0,0
S:G$mbstowcs$0$0({2}DF,SI:U),C,0,0
S:G$wcstombs$0$0({2}DF,SI:U),C,0,0
S:G$strcat$0$0({2}DF,DG,SC:S),C,0,0
S:G$strncat$0$0({2}DF,DG,SC:S),C,0,0
S:G$strcmp$0$0({2}DF,SC:S),C,0,0
S:G$strncmp$0$0({2}DF,SC:S),C,0,0
S:G$strcpy$0$0({2}DF,DG,SC:S),C,0,0
S:G$strncpy$0$0({2}DF,DG,SC:S),C,0,0
S:G$strlen$0$0({2}DF,SI:U),C,0,0
S:G$strchr$0$0({2}DF,DG,SC:S),C,0,0
S:G$strcspn$0$0({2}DF,SI:U),C,0,0
S:G$strpbrk$0$0({2}DF,DG,SC:S),C,0,0
S:G$strrchr$0$0({2}DF,DG,SC:S),C,0,0
S:G$strspn$0$0({2}DF,SI:U),C,0,0
S:G$strstr$0$0({2}DF,DG,SC:S),C,0,0
S:G$strtok$0$0({2}DF,DG,SC:S),C,0,0
S:G$memcmp$0$0({2}DF,SC:S),C,0,0
S:G$memchr$0$0({2}DF,DG,SV:S),C,0,0
S:G$memccpy$0$0({2}DF,DG,SV:S),C,0,0
S:G$memcpy$0$0({2}DF,DG,SV:S),C,0,0
S:G$memmove$0$0({2}DF,DG,SV:S),C,0,0
S:G$memset$0$0({2}DF,DG,SV:S),C,0,0
S:G$Fault_Detection$0$0({2}DF,SV:S),C,0,0
S:G$Fault_OverCurrent$0$0({2}DF,SV:S),C,0,0
S:G$Fault_Temperature$0$0({2}DF,SV:S),C,0,0
S:G$Fault_Voltage$0$0({2}DF,SV:S),C,0,0
S:G$Fault_Stall$0$0({2}DF,SV:S),C,0,0
S:G$Fault_PhaseLoss$0$0({2}DF,SV:S),C,0,0
S:G$UnderProcess$0$0({2}DF,SV:S),C,0,0
S:G$Fault_Power$0$0({2}DF,SV:S),C,0,0
S:G$AMP_Init$0$0({2}DF,SV:S),C,0,0
S:G$GPIO_Init$0$0({2}DF,SV:S),C,0,0
S:G$GPIO_Default_Init$0$0({2}DF,SV:S),C,0,0
S:G$SPI_Init$0$0({2}DF,SV:S),C,0,0
S:G$ADC_Init$0$0({2}DF,SV:S),C,0,0
S:G$TSD_Init$0$0({2}DF,SV:S),C,0,0
S:G$TEMP_Check$0$0({2}DF,SV:S),C,0,0
S:G$CMP0_Init$0$0({2}DF,SV:S),C,0,0
S:G$CMP3_Init$0$0({2}DF,SV:S),C,0,0
S:G$CMP3_Interrupt_Init$0$0({2}DF,SV:S),C,0,0
S:G$Driver_Init$0$0({2}DF,SV:S),C,0,0
S:G$UART1_Init$0$0({2}DF,SV:S),C,0,0
S:G$UART2_Init$0$0({2}DF,SV:S),C,0,0
S:G$put_char$0$0({2}DF,SV:S),C,0,0
S:G$put_string$0$0({2}DF,SV:S),C,0,0
S:G$CRC_Check$0$0({2}DF,SI:U),C,0,0
S:G$Timer1_Init$0$0({2}DF,SV:S),C,0,0
S:G$Timer2_Init$0$0({2}DF,SV:S),C,0,0
S:G$Timer3_Init$0$0({2}DF,SV:S),C,0,0
S:G$Timer4_Init$0$0({2}DF,SV:S),C,0,0
S:G$TIM4_Init_RF$0$0({2}DF,SV:S),C,0,0
S:G$SetPipe_DMA0$0$0({2}DF,SV:S),C,0,0
S:G$SetPipe_DMA1$0$0({2}DF,SV:S),C,0,0
S:G$SetDataPackage_DMA0$0$0({2}DF,SV:S),C,0,0
S:G$SetDataPackage_DMA1$0$0({2}DF,SV:S),C,0,0
S:G$EnableRun_DMA0$0$0({2}DF,SV:S),C,0,0
S:G$EnableRun_DMA1$0$0({2}DF,SV:S),C,0,0
S:G$GetStatus_DMA0$0$0({2}DF,:S),C,0,0
S:G$GetStatus_DMA1$0$0({2}DF,:S),C,0,0
S:G$SetEndian_DMA$0$0({2}DF,SV:S),C,0,0
S:G$SetIRQ_DMA$0$0({2}DF,SV:S),C,0,0
S:G$SetDbgMod_DMA$0$0({2}DF,SV:S),C,0,0
S:G$SetDbgData_DMA$0$0({2}DF,SV:S),C,0,0
S:G$Set_DBG_DMA$0$0({2}DF,SV:S),C,0,0
S:G$ReadFlashValue$0$0({2}DF,SC:U),C,0,0
S:G$Flash_WriteValue$0$0({2}DF,SC:U),C,0,0
S:G$GetWrite_Black_Addr$0$0({2}DF,SI:U),C,0,0
S:G$ReadFromFlash$0$0({2}DF,SV:S),C,0,0
S:G$Flash_Erase$0$0({2}DF,SV:S),C,0,0
S:G$Save_KeyValue$0$0({2}DF,SV:S),C,0,0
S:G$TargetRef_Process$0$0({2}DF,SV:S),C,0,0
S:G$Speed_response$0$0({2}DF,SV:S),C,0,0
S:G$Motor_Ramp$0$0({2}DF,SI:S),C,0,0
S:G$VSPSample$0$0({2}DF,SV:S),C,0,0
S:G$ONOFF_Starttest$0$0({2}DF,SV:S),C,0,0
S:G$ATORamp$0$0({2}DF,SV:S),C,0,0
S:G$TickCycle_1ms$0$0({2}DF,SV:S),C,0,0
S:G$PWMInputCapture$0$0({2}DF,SV:S),C,0,0
S:G$FGOutput$0$0({2}DF,SV:S),C,0,0
S:G$Fault_GetCurrentOffset$0$0({2}DF,SV:S),C,0,0
S:G$Abs_F32$0$0({2}DF,SL:U),C,0,0
S:G$SinCal$0$0({2}DF,SV:S),C,0,0
S:G$SqrtUDQ$0$0({2}DF,SI:U),C,0,0
S:G$zeroLoss$0$0({2}DF,SV:S),C,0,0
S:G$MC_Control$0$0({2}DF,SV:S),C,0,0
S:G$McTailWindDealwith$0$0({2}DF,SV:S),C,0,0
S:G$TailWindDealwith$0$0({2}DF,SV:S),C,0,0
S:G$FocDetectInit$0$0({2}DF,SV:S),C,0,0
S:G$FOCCloseLoopStart$0$0({2}DF,SV:S),C,0,0
S:G$FOC_TailWindDealwith$0$0({2}DF,SV:S),C,0,0
S:G$RsdProcess$0$0({2}DF,SV:S),C,0,0
S:G$RSDTailWindStart$0$0({2}DF,SV:S),C,0,0
S:G$RSDDetectInit$0$0({2}DF,SV:S),C,0,0
S:G$RSDStartProcess$0$0({2}DF,SC:U),C,0,0
S:G$RPD$0$0({2}DF,SV:S),C,0,0
S:G$RPD_Inject$0$0({2}DF,SV:S),C,0,0
S:G$RPD_Detect$0$0({2}DF,SV:S),C,0,0
S:G$RPD_Init$0$0({2}DF,SV:S),C,0,0
S:G$Time2_RPD_Init$0$0({2}DF,SV:S),C,0,0
S:G$RPD_0_VUWinit$0$0({2}DF,SV:S),C,0,0
S:G$RPD_1_WVinit$0$0({2}DF,SV:S),C,0,0
S:G$RPD_2_UVWinit$0$0({2}DF,SV:S),C,0,0
S:G$RPD_3_VUinit$0$0({2}DF,SV:S),C,0,0
S:G$RPD_4_WUVinit$0$0({2}DF,SV:S),C,0,0
S:G$RPD_5_UWinit$0$0({2}DF,SV:S),C,0,0
S:G$RPD_ZeroVector$0$0({2}DF,SV:S),C,0,0
S:G$RPD_Angle_Inject$0$0({2}DF,SV:S),C,0,0
S:G$RPD_Get_ID$0$0({2}DF,SV:S),C,0,0
S:G$Drv_SectionCheak$0$0({2}DF,SC:U),C,0,0
S:G$VariablesPreInit$0$0({2}DF,SV:S),C,0,0
S:G$GetCurrentOffset$0$0({2}DF,SV:S),C,0,0
S:G$Motor_Ready$0$0({2}DF,SV:S),C,0,0
S:G$Motor_Init$0$0({2}DF,SV:S),C,0,0
S:G$FOC_Init$0$0({2}DF,SV:S),C,0,0
S:G$Motor_Charge$0$0({2}DF,SV:S),C,0,0
S:G$MC_Stop$0$0({2}DF,SV:S),C,0,0
S:G$MC_Break$0$0({2}DF,SV:S),C,0,0
S:G$Motor_Static_Open$0$0({2}DF,SV:S),C,0,0
S:G$Motor_FocTailWind_Open$0$0({2}DF,SV:S),C,0,0
S:G$Motor_Align$0$0({2}DF,SV:S),C,0,0
S:G$MotorcontrolInit$0$0({2}DF,SV:S),C,0,0
S:G$Motor_TailWind$0$0({2}DF,SV:S),C,0,0
S:G$HW_One_PI$0$0({2}DF,SI:S),C,0,0
S:G$HW_One_PI2$0$0({2}DF,SI:S),C,0,0
S:G$HW_One_PI3$0$0({2}DF,SI:S),C,0,0
S:G$LPFFunction$0$0({2}DF,SI:S),C,0,0
S:G$Atan_Us_MDU$0$0({2}DF,SI:S),C,0,0
S:G$DivQ_L_MDU$0$0({2}DF,SI:S),C,0,0
S:G$Sqrt_alpbet$0$0({2}DF,SI:U),C,0,0
S:G$BEMFDetectInit$0$0({2}DF,SV:S),C,0,0
S:G$BemfProcess$0$0({2}DF,SV:S),C,0,0
S:G$BEMFFOCCloseLoopStart$0$0({2}DF,SV:S),C,0,0
S:G$Bemf_Start_Process$0$0({2}DF,SC:U),C,0,0
S:G$VoltageCompensation$0$0({2}DF,SV:S),C,0,0
S:G$BusAverageVoltage$0$0({2}DF,SV:S),C,0,0
S:G$LEDControl$0$0({2}DF,SV:S),C,0,0
S:G$Temperature_Control$0$0({2}DF,SV:S),C,0,0
S:G$LedDisplay$0$0({2}DF,SV:S),C,0,0
S:G$KeyInit$0$0({2}DF,SV:S),C,0,0
S:G$KeyValue$0$0({2}DF,SI:S),C,0,0
S:G$KeyValue1$0$0({2}DF,SI:S),C,0,0
S:G$KeyScan$0$0({2}DF,SV:S),C,0,0
S:G$ZeroCrossing_Init$0$0({2}DF,SV:S),C,0,0
S:G$FiledWeakenInit$0$0({2}DF,SV:S),C,0,0
S:G$FileWeakenControl$0$0({2}DF,SV:S),C,0,0
