ASxxxx Assembler V1.0.2.1  (Intel 8051), page 1.
Hexadecimal [24-Bits]

Symbol Table

    .__.$$$.                                                    =  002710 L
    .__.ABS.                                                    =  000000 G
    .__.CPU.                                                    =  000000 L
    .__.H$L.                                                    =  000001 L
    A                                                           =  0000E0 L
 16 A$TIMER$111                                                    000000 GR
 16 A$TIMER$124                                                    000001 GR
 16 A$TIMER$125                                                    000004 GR
 16 A$TIMER$126                                                    000005 GR
 16 A$TIMER$127                                                    000007 GR
 16 A$TIMER$132                                                    000008 GR
 16 A$TIMER$133                                                    000009 GR
 16 A$TIMER$134                                                    00000A GR
 16 A$TIMER$135                                                    00000C GR
 16 A$TIMER$136                                                    00000D GR
 16 A$TIMER$137                                                    00000E GR
 16 A$TIMER$138                                                    000011 GR
 16 A$TIMER$139                                                    000014 GR
 16 A$TIMER$140                                                    000015 GR
 16 A$TIMER$143                                                    000016 GR
 16 A$TIMER$146                                                    000019 GR
 16 A$TIMER$149                                                    00001C GR
 16 A$TIMER$152                                                    00001F GR
 16 A$TIMER$155                                                    000022 GR
 16 A$TIMER$158                                                    000025 GR
 16 A$TIMER$161                                                    000028 GR
 16 A$TIMER$164                                                    00002B GR
 16 A$TIMER$167                                                    00002E GR
 16 A$TIMER$170                                                    000031 GR
 16 A$TIMER$173                                                    000034 GR
 16 A$TIMER$176                                                    000037 GR
 16 A$TIMER$181                                                    00003A GR
    A.0                                                         =  0000E0 L
    A.1                                                         =  0000E1 L
    A.2                                                         =  0000E2 L
    A.3                                                         =  0000E3 L
    A.4                                                         =  0000E4 L
    A.5                                                         =  0000E5 L
    A.6                                                         =  0000E6 L
    A.7                                                         =  0000E7 L
    AC                                                          =  0000D6 L
    ACC                                                         =  0000E0 L
    ACC.0                                                       =  0000E0 L
    ACC.1                                                       =  0000E1 L
    ACC.2                                                       =  0000E2 L
    ACC.3                                                       =  0000E3 L
    ACC.4                                                       =  0000E4 L
    ACC.5                                                       =  0000E5 L
    ACC.6                                                       =  0000E6 L
    ACC.7                                                       =  0000E7 L
    B                                                           =  0000F0 L
    B.0                                                         =  0000F0 L
    B.1                                                         =  0000F1 L
    B.2                                                         =  0000F2 L
    B.3                                                         =  0000F3 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 2.
Hexadecimal [24-Bits]

Symbol Table

    B.4                                                         =  0000F4 L
    B.5                                                         =  0000F5 L
    B.6                                                         =  0000F6 L
    B.7                                                         =  0000F7 L
 16 C$TIMER.c$14$0$242                                          =  000000 GR
 16 C$TIMER.c$60$1$246                                          =  000001 GR
 16 C$TIMER.c$62$1$246                                          =  000001 GR
 16 C$TIMER.c$64$1$246                                          =  000008 GR
 16 C$TIMER.c$65$1$246                                          =  000008 GR
 16 C$TIMER.c$67$1$246                                          =  000016 GR
 16 C$TIMER.c$68$1$246                                          =  000019 GR
 16 C$TIMER.c$69$1$246                                          =  00001C GR
 16 C$TIMER.c$71$1$246                                          =  00001F GR
 16 C$TIMER.c$73$1$246                                          =  000022 GR
 16 C$TIMER.c$74$1$246                                          =  000025 GR
 16 C$TIMER.c$76$1$246                                          =  000028 GR
 16 C$TIMER.c$77$1$246                                          =  00002B GR
 16 C$TIMER.c$79$1$246                                          =  00002E GR
 16 C$TIMER.c$80$1$246                                          =  000031 GR
 16 C$TIMER.c$87$1$246                                          =  000034 GR
 16 C$TIMER.c$88$1$246                                          =  000037 GR
 16 C$TIMER.c$89$1$246                                          =  00003A GR
    CPRL2                                                       =  0000C8 L
    CT2                                                         =  0000C9 L
    CY                                                          =  0000D7 L
    DPH                                                         =  000083 L
    DPL                                                         =  000082 L
    EA                                                          =  0000AF L
    ES                                                          =  0000AC L
    ET0                                                         =  0000A9 L
    ET1                                                         =  0000AB L
    ET2                                                         =  0000AD L
    EX0                                                         =  0000A8 L
    EX1                                                         =  0000AA L
    EXEN2                                                       =  0000CB L
    EXF2                                                        =  0000CE L
    F0                                                          =  0000D5 L
    G$TIM3_CR0$0$0                                              =  00009C G
    G$TIM3_CR1$0$0                                              =  00009D G
 16 G$Timer1_Init$0$0                                           =  000000 GR
 16 G$Timer3_Init$0$0                                           =  000001 GR
    IE                                                          =  0000A8 L
    IE.0                                                        =  0000A8 L
    IE.1                                                        =  0000A9 L
    IE.2                                                        =  0000AA L
    IE.3                                                        =  0000AB L
    IE.4                                                        =  0000AC L
    IE.5                                                        =  0000AD L
    IE.7                                                        =  0000AF L
    IE0                                                         =  000089 L
    IE1                                                         =  00008B L
    INT0                                                        =  0000B2 L
    INT1                                                        =  0000B3 L
    IP                                                          =  0000B8 L
    IP.0                                                        =  0000B8 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 3.
Hexadecimal [24-Bits]

Symbol Table

    IP.1                                                        =  0000B9 L
    IP.2                                                        =  0000BA L
    IP.3                                                        =  0000BB L
    IP.4                                                        =  0000BC L
    IP.5                                                        =  0000BD L
    IT0                                                         =  000088 L
    IT1                                                         =  00008A L
    OV                                                          =  0000D2 L
    P                                                           =  0000D0 L
    P0                                                          =  000080 L
    P0.0                                                        =  000080 L
    P0.1                                                        =  000081 L
    P0.2                                                        =  000082 L
    P0.3                                                        =  000083 L
    P0.4                                                        =  000084 L
    P0.5                                                        =  000085 L
    P0.6                                                        =  000086 L
    P0.7                                                        =  000087 L
    P1                                                          =  000090 L
    P1.0                                                        =  000090 L
    P1.1                                                        =  000091 L
    P1.2                                                        =  000092 L
    P1.3                                                        =  000093 L
    P1.4                                                        =  000094 L
    P1.5                                                        =  000095 L
    P1.6                                                        =  000096 L
    P1.7                                                        =  000097 L
    P2                                                          =  0000A0 L
    P2.0                                                        =  0000A0 L
    P2.1                                                        =  0000A1 L
    P2.2                                                        =  0000A2 L
    P2.3                                                        =  0000A3 L
    P2.4                                                        =  0000A4 L
    P2.5                                                        =  0000A5 L
    P2.6                                                        =  0000A6 L
    P2.7                                                        =  0000A7 L
    P3                                                          =  0000B0 L
    P3.0                                                        =  0000B0 L
    P3.1                                                        =  0000B1 L
    P3.2                                                        =  0000B2 L
    P3.3                                                        =  0000B3 L
    P3.4                                                        =  0000B4 L
    P3.5                                                        =  0000B5 L
    P3.6                                                        =  0000B6 L
    P3.7                                                        =  0000B7 L
    PCON                                                        =  000087 L
    PS                                                          =  0000BC L
    PSW                                                         =  0000D0 L
    PSW.0                                                       =  0000D0 L
    PSW.1                                                       =  0000D1 L
    PSW.2                                                       =  0000D2 L
    PSW.3                                                       =  0000D3 L
    PSW.4                                                       =  0000D4 L
    PSW.5                                                       =  0000D5 L
    PSW.6                                                       =  0000D6 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 4.
Hexadecimal [24-Bits]

Symbol Table

    PSW.7                                                       =  0000D7 L
    PT0                                                         =  0000B9 L
    PT1                                                         =  0000BB L
    PT2                                                         =  0000BD L
    PX0                                                         =  0000B8 L
    PX1                                                         =  0000BA L
    RB8                                                         =  00009A L
    RCAP2H                                                      =  0000CB L
    RCAP2L                                                      =  0000CA L
    RCLK                                                        =  0000CD L
    REN                                                         =  00009C L
    RI                                                          =  000098 L
    RS0                                                         =  0000D3 L
    RS1                                                         =  0000D4 L
    RXD                                                         =  0000B0 L
    SBUF                                                        =  000099 L
    SCON                                                        =  000098 L
    SCON.0                                                      =  000098 L
    SCON.1                                                      =  000099 L
    SCON.2                                                      =  00009A L
    SCON.3                                                      =  00009B L
    SCON.4                                                      =  00009C L
    SCON.5                                                      =  00009D L
    SCON.6                                                      =  00009E L
    SCON.7                                                      =  00009F L
    SM0                                                         =  00009F L
    SM1                                                         =  00009E L
    SM2                                                         =  00009D L
    SP                                                          =  000081 L
    T2CON                                                       =  0000C8 L
    T2CON.0                                                     =  0000C8 L
    T2CON.1                                                     =  0000C9 L
    T2CON.2                                                     =  0000CA L
    T2CON.3                                                     =  0000CB L
    T2CON.4                                                     =  0000CC L
    T2CON.5                                                     =  0000CD L
    T2CON.6                                                     =  0000CE L
    T2CON.7                                                     =  0000CF L
    TB8                                                         =  00009B L
    TCLK                                                        =  0000CC L
    TCON                                                        =  000088 L
    TCON.0                                                      =  000088 L
    TCON.1                                                      =  000089 L
    TCON.2                                                      =  00008A L
    TCON.3                                                      =  00008B L
    TCON.4                                                      =  00008C L
    TCON.5                                                      =  00008D L
    TCON.6                                                      =  00008E L
    TCON.7                                                      =  00008F L
    TF0                                                         =  00008D L
    TF1                                                         =  00008F L
    TF2                                                         =  0000CF L
    TH0                                                         =  00008C L
    TH1                                                         =  00008D L
    TH2                                                         =  0000CD L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 5.
Hexadecimal [24-Bits]

Symbol Table

    TI                                                          =  000099 L
    TL0                                                         =  00008A L
    TL1                                                         =  00008B L
    TL2                                                         =  0000CC L
    TMOD                                                        =  000089 L
    TR0                                                         =  00008C L
    TR1                                                         =  00008E L
    TR2                                                         =  0000CA L
    TXD                                                         =  0000B1 L
 16 XG$Timer3_Init$0$0                                          =  00003A GR
    _TIM3_CR0                                                   =  00009C G
    _TIM3_CR1                                                   =  00009D G
 16 _Timer1_Init                                                   000000 R
 16 _Timer3_Init                                                   000001 GR
    a                                                           =  0000E0 L
    a.0                                                         =  0000E0 L
    a.1                                                         =  0000E1 L
    a.2                                                         =  0000E2 L
    a.3                                                         =  0000E3 L
    a.4                                                         =  0000E4 L
    a.5                                                         =  0000E5 L
    a.6                                                         =  0000E6 L
    a.7                                                         =  0000E7 L
    ac                                                          =  0000D6 L
    acc                                                         =  0000E0 L
    acc.0                                                       =  0000E0 L
    acc.1                                                       =  0000E1 L
    acc.2                                                       =  0000E2 L
    acc.3                                                       =  0000E3 L
    acc.4                                                       =  0000E4 L
    acc.5                                                       =  0000E5 L
    acc.6                                                       =  0000E6 L
    acc.7                                                       =  0000E7 L
    ar0                                                         =  000000 
    ar1                                                         =  000001 
    ar2                                                         =  000002 
    ar3                                                         =  000003 
    ar4                                                         =  000004 
    ar5                                                         =  000005 
    ar6                                                         =  000006 
    ar7                                                         =  000007 
    b                                                           =  0000F0 L
    b.0                                                         =  0000F0 L
    b.1                                                         =  0000F1 L
    b.2                                                         =  0000F2 L
    b.3                                                         =  0000F3 L
    b.4                                                         =  0000F4 L
    b.5                                                         =  0000F5 L
    b.6                                                         =  0000F6 L
    b.7                                                         =  0000F7 L
    cprl2                                                       =  0000C8 L
    ct2                                                         =  0000C9 L
    cy                                                          =  0000D7 L
    dph                                                         =  000083 L
    dpl                                                         =  000082 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 6.
Hexadecimal [24-Bits]

Symbol Table

    ea                                                          =  0000AF L
    es                                                          =  0000AC L
    et0                                                         =  0000A9 L
    et1                                                         =  0000AB L
    et2                                                         =  0000AD L
    ex0                                                         =  0000A8 L
    ex1                                                         =  0000AA L
    exen2                                                       =  0000CB L
    exf2                                                        =  0000CE L
    f0                                                          =  0000D5 L
    ie                                                          =  0000A8 L
    ie.0                                                        =  0000A8 L
    ie.1                                                        =  0000A9 L
    ie.2                                                        =  0000AA L
    ie.3                                                        =  0000AB L
    ie.4                                                        =  0000AC L
    ie.5                                                        =  0000AD L
    ie.7                                                        =  0000AF L
    ie0                                                         =  000089 L
    ie1                                                         =  00008B L
    int0                                                        =  0000B2 L
    int1                                                        =  0000B3 L
    ip                                                          =  0000B8 L
    ip.0                                                        =  0000B8 L
    ip.1                                                        =  0000B9 L
    ip.2                                                        =  0000BA L
    ip.3                                                        =  0000BB L
    ip.4                                                        =  0000BC L
    ip.5                                                        =  0000BD L
    it0                                                         =  000088 L
    it1                                                         =  00008A L
    ov                                                          =  0000D2 L
    p                                                           =  0000D0 L
    p0                                                          =  000080 L
    p0.0                                                        =  000080 L
    p0.1                                                        =  000081 L
    p0.2                                                        =  000082 L
    p0.3                                                        =  000083 L
    p0.4                                                        =  000084 L
    p0.5                                                        =  000085 L
    p0.6                                                        =  000086 L
    p0.7                                                        =  000087 L
    p1                                                          =  000090 L
    p1.0                                                        =  000090 L
    p1.1                                                        =  000091 L
    p1.2                                                        =  000092 L
    p1.3                                                        =  000093 L
    p1.4                                                        =  000094 L
    p1.5                                                        =  000095 L
    p1.6                                                        =  000096 L
    p1.7                                                        =  000097 L
    p2                                                          =  0000A0 L
    p2.0                                                        =  0000A0 L
    p2.1                                                        =  0000A1 L
    p2.2                                                        =  0000A2 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 7.
Hexadecimal [24-Bits]

Symbol Table

    p2.3                                                        =  0000A3 L
    p2.4                                                        =  0000A4 L
    p2.5                                                        =  0000A5 L
    p2.6                                                        =  0000A6 L
    p2.7                                                        =  0000A7 L
    p3                                                          =  0000B0 L
    p3.0                                                        =  0000B0 L
    p3.1                                                        =  0000B1 L
    p3.2                                                        =  0000B2 L
    p3.3                                                        =  0000B3 L
    p3.4                                                        =  0000B4 L
    p3.5                                                        =  0000B5 L
    p3.6                                                        =  0000B6 L
    p3.7                                                        =  0000B7 L
    pcon                                                        =  000087 L
    ps                                                          =  0000BC L
    psw                                                         =  0000D0 L
    psw.0                                                       =  0000D0 L
    psw.1                                                       =  0000D1 L
    psw.2                                                       =  0000D2 L
    psw.3                                                       =  0000D3 L
    psw.4                                                       =  0000D4 L
    psw.5                                                       =  0000D5 L
    psw.6                                                       =  0000D6 L
    psw.7                                                       =  0000D7 L
    pt0                                                         =  0000B9 L
    pt1                                                         =  0000BB L
    pt2                                                         =  0000BD L
    px0                                                         =  0000B8 L
    px1                                                         =  0000BA L
    rb8                                                         =  00009A L
    rcap2h                                                      =  0000CB L
    rcap2l                                                      =  0000CA L
    rclk                                                        =  0000CD L
    ren                                                         =  00009C L
    ri                                                          =  000098 L
    rs0                                                         =  0000D3 L
    rs1                                                         =  0000D4 L
    rxd                                                         =  0000B0 L
    sbuf                                                        =  000099 L
    scon                                                        =  000098 L
    scon.0                                                      =  000098 L
    scon.1                                                      =  000099 L
    scon.2                                                      =  00009A L
    scon.3                                                      =  00009B L
    scon.4                                                      =  00009C L
    scon.5                                                      =  00009D L
    scon.6                                                      =  00009E L
    scon.7                                                      =  00009F L
    sm0                                                         =  00009F L
    sm1                                                         =  00009E L
    sm2                                                         =  00009D L
    sp                                                          =  000081 L
    t2con                                                       =  0000C8 L
    t2con.0                                                     =  0000C8 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 8.
Hexadecimal [24-Bits]

Symbol Table

    t2con.1                                                     =  0000C9 L
    t2con.2                                                     =  0000CA L
    t2con.3                                                     =  0000CB L
    t2con.4                                                     =  0000CC L
    t2con.5                                                     =  0000CD L
    t2con.6                                                     =  0000CE L
    t2con.7                                                     =  0000CF L
    tb8                                                         =  00009B L
    tclk                                                        =  0000CC L
    tcon                                                        =  000088 L
    tcon.0                                                      =  000088 L
    tcon.1                                                      =  000089 L
    tcon.2                                                      =  00008A L
    tcon.3                                                      =  00008B L
    tcon.4                                                      =  00008C L
    tcon.5                                                      =  00008D L
    tcon.6                                                      =  00008E L
    tcon.7                                                      =  00008F L
    tf0                                                         =  00008D L
    tf1                                                         =  00008F L
    tf2                                                         =  0000CF L
    th0                                                         =  00008C L
    th1                                                         =  00008D L
    th2                                                         =  0000CD L
    ti                                                          =  000099 L
    tl0                                                         =  00008A L
    tl1                                                         =  00008B L
    tl2                                                         =  0000CC L
    tmod                                                        =  000089 L
    tr0                                                         =  00008C L
    tr1                                                         =  00008E L
    tr2                                                         =  0000CA L
    txd                                                         =  0000B1 L


ASxxxx Assembler V1.0.2.1  (Intel 8051), page 9.
Hexadecimal [24-Bits]

Area Table

   0 _CODE                                      size      0   flags    0
   1 RSEG                                       size      0   flags    8
   2 RSEG0                                      size      0   flags    8
   3 RSEG1                                      size      0   flags    8
   4 REG_BANK_0                                 size      8   flags    4
   5 DSEG                                       size      0   flags    0
   6 ISEG                                       size      0   flags    0
   7 IABS                                       size      0   flags    8
   8 BSEG                                       size      0   flags   80
   9 PSEG                                       size      0   flags   50
   A XSEG                                       size      0   flags   40
   B XABS                                       size      0   flags   48
   C XISEG                                      size      0   flags   40
   D HOME                                       size      0   flags   20
   E GSINIT0                                    size      0   flags   20
   F GSINIT1                                    size      0   flags   20
  10 GSINIT2                                    size      0   flags   20
  11 GSINIT3                                    size      0   flags   20
  12 GSINIT4                                    size      0   flags   20
  13 GSINIT5                                    size      0   flags   20
  14 GSINIT                                     size      0   flags   20
  15 GSFINAL                                    size      0   flags   20
  16 CSEG                                       size     3B   flags   20
  17 CONST                                      size      0   flags   20
  18 XINIT                                      size      0   flags   20
  19 CABS                                       size      0   flags   28

