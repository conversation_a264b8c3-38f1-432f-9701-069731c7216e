/**************************** (C) COPYRIGHT 2018 Fortiortech shenzhen *****************************
* File Name          : FOCTailDect.h
* Author             : Bruce, Fortiortech Hardware
* Version            : V1.0
* Date               : 2017-12-27
* Description        : This file contains all the common data types used for
*                      Motor Control.
***************************************************************************************************
* All Rights Reserved
**************************************************************************************************/
#ifndef __FOCTAILDETECT_H_
#define __FOCTAILDETECT_H_

#include "FU65xx_2.h"
#include "Customer.h"
#include <MotorControl.h>
/****************************FOC TaildWind参数变量**************************/


extern void FocDetectInit(void);


extern void FOCCloseLoopStart(void);
extern void FOC_TailWindDealwith(void);
#endif