ASxxxx Assembler V1.0.2.1  (Intel 8051), page 1.
Hexadecimal [24-Bits]

Symbol Table

    .__.$$$.                                                    =  002710 L
    .__.ABS.                                                    =  000000 G
    .__.CPU.                                                    =  000000 L
    .__.H$L.                                                    =  000001 L
    A                                                           =  0000E0 L
 16 A$MotorControl$121                                             000000 GR
 16 A$MotorControl$122                                             000003 GR
 16 A$MotorControl$123                                             000004 GR
 16 A$MotorControl$124                                             000006 GR
 16 A$MotorControl$125                                             000009 GR
 16 A$MotorControl$126                                             00000A GR
 16 A$MotorControl$127                                             00000B GR
 16 A$MotorControl$128                                             00000C GR
 16 A$MotorControl$129                                             00000D GR
 16 A$MotorControl$130                                             00000E GR
 16 A$MotorControl$131                                             00000F GR
 16 A$MotorControl$132                                             000011 GR
 16 A$MotorControl$133                                             000012 GR
 16 A$MotorControl$134                                             000014 GR
 16 A$MotorControl$135                                             000015 GR
 16 A$MotorControl$138                                             000017 GR
 16 A$MotorControl$141                                             00001A GR
 16 A$MotorControl$142                                             00001D GR
 16 A$MotorControl$143                                             00001E GR
 16 A$MotorControl$147                                             00001F GR
 16 A$MotorControl$148                                             000021 GR
 16 A$MotorControl$149                                             000023 GR
 16 A$MotorControl$150                                             000025 GR
 16 A$MotorControl$152                                             000028 GR
 16 A$MotorControl$153                                             00002A GR
 16 A$MotorControl$154                                             00002C GR
 16 A$MotorControl$155                                             00002D GR
 16 A$MotorControl$156                                             00002F GR
 16 A$MotorControl$157                                             000031 GR
 16 A$MotorControl$158                                             000033 GR
 16 A$MotorControl$159                                             000034 GR
 16 A$MotorControl$160                                             000036 GR
 16 A$MotorControl$161                                             000037 GR
 16 A$MotorControl$191                                             00004E GR
 16 A$MotorControl$194                                             000051 GR
 16 A$MotorControl$195                                             000053 GR
 16 A$MotorControl$198                                             000055 GR
 16 A$MotorControl$199                                             000058 GR
 16 A$MotorControl$203                                             000059 GR
 16 A$MotorControl$204                                             00005C GR
 16 A$MotorControl$205                                             00005D GR
 16 A$MotorControl$206                                             00005E GR
 16 A$MotorControl$207                                             000061 GR
 16 A$MotorControl$209                                             000063 GR
 16 A$MotorControl$211                                             000064 GR
 16 A$MotorControl$212                                             000067 GR
 16 A$MotorControl$216                                             000068 GR
 16 A$MotorControl$219                                             00006B GR
 16 A$MotorControl$220                                             00006E GR
 16 A$MotorControl$221                                             00006F GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 2.
Hexadecimal [24-Bits]

Symbol Table

 16 A$MotorControl$224                                             000070 GR
 16 A$MotorControl$225                                             000073 GR
 16 A$MotorControl$226                                             000074 GR
 16 A$MotorControl$227                                             000075 GR
 16 A$MotorControl$230                                             000076 GR
 16 A$MotorControl$231                                             000079 GR
 16 A$MotorControl$232                                             00007A GR
 16 A$MotorControl$235                                             00007B GR
 16 A$MotorControl$241                                             00007C GR
 16 A$MotorControl$242                                             00007E GR
 16 A$MotorControl$245                                             000080 GR
 16 A$MotorControl$246                                             000083 GR
 16 A$MotorControl$250                                             000084 GR
 16 A$MotorControl$253                                             000087 GR
 16 A$MotorControl$254                                             00008A GR
 16 A$MotorControl$258                                             00008B GR
 16 A$MotorControl$259                                             00008E GR
 16 A$MotorControl$260                                             00008F GR
 16 A$MotorControl$261                                             000090 GR
 16 A$MotorControl$262                                             000093 GR
 16 A$MotorControl$264                                             000095 GR
 16 A$MotorControl$268                                             000096 GR
 16 A$MotorControl$271                                             000099 GR
 16 A$MotorControl$272                                             00009C GR
 16 A$MotorControl$273                                             00009D GR
 16 A$MotorControl$274                                             00009E GR
 16 A$MotorControl$275                                             0000A0 GR
 16 A$MotorControl$276                                             0000A1 GR
 16 A$MotorControl$279                                             0000A2 GR
 16 A$MotorControl$282                                             0000A5 GR
 16 A$MotorControl$288                                             0000A6 GR
 16 A$MotorControl$289                                             0000A8 GR
 16 A$MotorControl$292                                             0000AA GR
 16 A$MotorControl$293                                             0000AD GR
 16 A$MotorControl$297                                             0000AE GR
 16 A$MotorControl$300                                             0000B1 GR
 16 A$MotorControl$301                                             0000B4 GR
 16 A$MotorControl$305                                             0000B5 GR
 16 A$MotorControl$308                                             0000B8 GR
 16 A$MotorControl$309                                             0000BB GR
 16 A$MotorControl$310                                             0000BC GR
 16 A$MotorControl$311                                             0000BD GR
 16 A$MotorControl$312                                             0000BE GR
 16 A$MotorControl$313                                             0000BF GR
 16 A$MotorControl$314                                             0000C0 GR
 16 A$MotorControl$315                                             0000C1 GR
 16 A$MotorControl$316                                             0000C3 GR
 16 A$MotorControl$320                                             0000C4 GR
 16 A$MotorControl$323                                             0000C6 GR
 16 A$MotorControl$326                                             0000C9 GR
 16 A$MotorControl$332                                             0000CA GR
 16 A$MotorControl$333                                             0000CD GR
 16 A$MotorControl$334                                             0000CE GR
 16 A$MotorControl$335                                             0000CF GR
 16 A$MotorControl$336                                             0000D2 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 3.
Hexadecimal [24-Bits]

Symbol Table

 16 A$MotorControl$340                                             0000D3 GR
 16 A$MotorControl$343                                             0000D6 GR
 16 A$MotorControl$344                                             0000D9 GR
 16 A$MotorControl$345                                             0000DA GR
 16 A$MotorControl$346                                             0000DB GR
 16 A$MotorControl$347                                             0000DC GR
 16 A$MotorControl$348                                             0000DD GR
 16 A$MotorControl$349                                             0000DE GR
 16 A$MotorControl$350                                             0000DF GR
 16 A$MotorControl$353                                             0000E1 GR
 16 A$MotorControl$357                                             0000E4 GR
 16 A$MotorControl$358                                             0000E7 GR
 16 A$MotorControl$359                                             0000E8 GR
 16 A$MotorControl$360                                             0000E9 GR
 16 A$MotorControl$361                                             0000EB GR
 16 A$MotorControl$362                                             0000EC GR
 16 A$MotorControl$365                                             0000ED GR
 16 A$MotorControl$366                                             0000F0 GR
 16 A$MotorControl$370                                             0000F1 GR
 16 A$MotorControl$373                                             0000F4 GR
 16 A$MotorControl$376                                             0000F7 GR
 16 A$MotorControl$382                                             0000F8 GR
 16 A$MotorControl$383                                             0000FA GR
 16 A$MotorControl$386                                             0000FC GR
 16 A$MotorControl$387                                             0000FF GR
 16 A$MotorControl$391                                             000100 GR
 16 A$MotorControl$392                                             000103 GR
 16 A$MotorControl$396                                             000104 GR
 16 A$MotorControl$399                                             000107 GR
 16 A$MotorControl$400                                             00010A GR
 16 A$MotorControl$401                                             00010B GR
 16 A$MotorControl$402                                             00010C GR
 16 A$MotorControl$403                                             00010E GR
 16 A$MotorControl$404                                             00010F GR
 16 A$MotorControl$407                                             000110 GR
 16 A$MotorControl$413                                             000111 GR
 16 A$MotorControl$414                                             000113 GR
 16 A$MotorControl$417                                             000115 GR
 16 A$MotorControl$418                                             000118 GR
 16 A$MotorControl$422                                             000119 GR
 16 A$MotorControl$430                                             00011C GR
 16 A$MotorControl$431                                             00011E GR
 16 A$MotorControl$434                                             000120 GR
 16 A$MotorControl$438                                             000123 GR
 16 A$MotorControl$439                                             000126 GR
 16 A$MotorControl$440                                             000127 GR
 16 A$MotorControl$441                                             000128 GR
 16 A$MotorControl$442                                             000129 GR
 16 A$MotorControl$443                                             00012A GR
 16 A$MotorControl$444                                             00012B GR
 16 A$MotorControl$445                                             00012C GR
 16 A$MotorControl$448                                             00012E GR
 16 A$MotorControl$449                                             000131 GR
 16 A$MotorControl$450                                             000132 GR
 16 A$MotorControl$451                                             000133 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 4.
Hexadecimal [24-Bits]

Symbol Table

 16 A$MotorControl$452                                             000134 GR
 16 A$MotorControl$455                                             000135 GR
 16 A$MotorControl$456                                             000138 GR
 16 A$MotorControl$459                                             000139 GR
 16 A$MotorControl$462                                             00013B GR
 16 A$MotorControl$465                                             00013E GR
 16 A$MotorControl$466                                             000141 GR
 16 A$MotorControl$467                                             000142 GR
 16 A$MotorControl$468                                             000144 GR
 16 A$MotorControl$469                                             000145 GR
 16 A$MotorControl$472                                             000146 GR
 16 A$MotorControl$475                                             000149 GR
 16 A$MotorControl$476                                             00014C GR
 16 A$MotorControl$477                                             00014D GR
 16 A$MotorControl$478                                             00014E GR
 16 A$MotorControl$479                                             000150 GR
 16 A$MotorControl$480                                             000151 GR
 16 A$MotorControl$485                                             000152 GR
 16 A$MotorControl$489                                             000153 GR
 16 A$MotorControl$490                                             000155 GR
 16 A$MotorControl$493                                             000158 GR
 16 A$MotorControl$496                                             00015B GR
 16 A$MotorControl$500                                             00015E GR
 16 A$MotorControl$503                                             000160 GR
 16 A$MotorControl$504                                             000163 GR
 16 A$MotorControl$505                                             000164 GR
 16 A$MotorControl$506                                             000166 GR
 16 A$MotorControl$507                                             000167 GR
 16 A$MotorControl$510                                             000168 GR
 16 A$MotorControl$513                                             00016A GR
 16 A$MotorControl$514                                             00016C GR
 16 A$MotorControl$519                                             00016E GR
 16 A$MotorControl$523                                             00016F GR
 16 A$MotorControl$531                                             000172 GR
    A.0                                                         =  0000E0 L
    A.1                                                         =  0000E1 L
    A.2                                                         =  0000E2 L
    A.3                                                         =  0000E3 L
    A.4                                                         =  0000E4 L
    A.5                                                         =  0000E5 L
    A.6                                                         =  0000E6 L
    A.7                                                         =  0000E7 L
    AC                                                          =  0000D6 L
    ACC                                                         =  0000E0 L
    ACC.0                                                       =  0000E0 L
    ACC.1                                                       =  0000E1 L
    ACC.2                                                       =  0000E2 L
    ACC.3                                                       =  0000E3 L
    ACC.4                                                       =  0000E4 L
    ACC.5                                                       =  0000E5 L
    ACC.6                                                       =  0000E6 L
    ACC.7                                                       =  0000E7 L
    B                                                           =  0000F0 L
    B.0                                                         =  0000F0 L
    B.1                                                         =  0000F1 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 5.
Hexadecimal [24-Bits]

Symbol Table

    B.2                                                         =  0000F2 L
    B.3                                                         =  0000F3 L
    B.4                                                         =  0000F4 L
    B.5                                                         =  0000F5 L
    B.6                                                         =  0000F6 L
    B.7                                                         =  0000F7 L
 16 C$MotorControl.c$101$3$252                                  =  0000B1 GR
 16 C$MotorControl.c$105$3$253                                  =  0000B5 GR
 16 C$MotorControl.c$112$3$253                                  =  0000B8 GR
 16 C$MotorControl.c$114$4$254                                  =  0000C4 GR
 16 C$MotorControl.c$126$5$255                                  =  0000C6 GR
 16 C$MotorControl.c$138$2$244                                  =  0000C9 GR
 16 C$MotorControl.c$214$2$244                                  =  0000CA GR
 16 C$MotorControl.c$215$2$244                                  =  0000CA GR
 16 C$MotorControl.c$22$0$241                                   =  000000 GR
 16 C$MotorControl.c$226$2$244                                  =  0000D3 GR
 16 C$MotorControl.c$228$3$257                                  =  0000D6 GR
 16 C$MotorControl.c$231$4$258                                  =  0000E1 GR
 16 C$MotorControl.c$235$4$259                                  =  0000E4 GR
 16 C$MotorControl.c$236$4$259                                  =  0000ED GR
 16 C$MotorControl.c$241$3$260                                  =  0000F1 GR
 16 C$MotorControl.c$242$3$260                                  =  0000F4 GR
 16 C$MotorControl.c$245$2$244                                  =  0000F7 GR
 16 C$MotorControl.c$247$2$244                                  =  0000F8 GR
 16 C$MotorControl.c$248$2$244                                  =  0000F8 GR
 16 C$MotorControl.c$250$3$261                                  =  0000FC GR
 16 C$MotorControl.c$252$2$244                                  =  000100 GR
 16 C$MotorControl.c$254$3$262                                  =  000104 GR
 16 C$MotorControl.c$255$3$262                                  =  000107 GR
 16 C$MotorControl.c$261$2$244                                  =  000110 GR
 16 C$MotorControl.c$263$2$244                                  =  000111 GR
 16 C$MotorControl.c$264$2$244                                  =  000111 GR
 16 C$MotorControl.c$266$3$264                                  =  000115 GR
 16 C$MotorControl.c$27$2$242                                   =  000000 GR
 16 C$MotorControl.c$270$3$265                                  =  000119 GR
 16 C$MotorControl.c$273$2$244                                  =  00011C GR
 16 C$MotorControl.c$275$2$244                                  =  00011C GR
 16 C$MotorControl.c$276$2$244                                  =  00011C GR
 16 C$MotorControl.c$278$3$266                                  =  000120 GR
 16 C$MotorControl.c$280$2$244                                  =  000123 GR
 16 C$MotorControl.c$282$3$267                                  =  00012E GR
 16 C$MotorControl.c$283$3$267                                  =  000135 GR
 16 C$MotorControl.c$284$3$267                                  =  000139 GR
 16 C$MotorControl.c$285$3$267                                  =  00013B GR
 16 C$MotorControl.c$286$3$267                                  =  00013E GR
 16 C$MotorControl.c$287$3$267                                  =  000146 GR
 16 C$MotorControl.c$288$3$267                                  =  000149 GR
 16 C$MotorControl.c$29$3$243                                   =  000017 GR
 16 C$MotorControl.c$291$2$244                                  =  000152 GR
 16 C$MotorControl.c$293$2$244                                  =  000152 GR
 16 C$MotorControl.c$294$2$244                                  =  000153 GR
 16 C$MotorControl.c$296$3$268                                  =  000158 GR
 16 C$MotorControl.c$297$3$268                                  =  00015B GR
 16 C$MotorControl.c$30$3$243                                   =  00001A GR
 16 C$MotorControl.c$301$3$269                                  =  00015E GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 6.
Hexadecimal [24-Bits]

Symbol Table

 16 C$MotorControl.c$302$3$269                                  =  000160 GR
 16 C$MotorControl.c$304$3$269                                  =  000168 GR
 16 C$MotorControl.c$306$4$270                                  =  00016A GR
 16 C$MotorControl.c$310$2$244                                  =  00016E GR
 16 C$MotorControl.c$312$2$244                                  =  00016E GR
 16 C$MotorControl.c$313$2$244                                  =  00016F GR
 16 C$MotorControl.c$315$1$241                                  =  000172 GR
 16 C$MotorControl.c$316$1$241                                  =  000172 GR
 16 C$MotorControl.c$35$1$241                                   =  00001F GR
 16 C$MotorControl.c$37$2$244                                   =  00004E GR
 16 C$MotorControl.c$38$2$244                                   =  00004E GR
 16 C$MotorControl.c$40$2$244                                   =  000051 GR
 16 C$MotorControl.c$42$3$245                                   =  000055 GR
 16 C$MotorControl.c$44$2$244                                   =  000059 GR
 16 C$MotorControl.c$46$3$246                                   =  000068 GR
 16 C$MotorControl.c$47$3$246                                   =  00006B GR
 16 C$MotorControl.c$48$3$246                                   =  000070 GR
 16 C$MotorControl.c$49$3$246                                   =  000076 GR
 16 C$MotorControl.c$55$2$244                                   =  00007B GR
 16 C$MotorControl.c$57$2$244                                   =  00007C GR
 16 C$MotorControl.c$58$2$244                                   =  00007C GR
 16 C$MotorControl.c$60$3$248                                   =  000080 GR
 16 C$MotorControl.c$62$2$244                                   =  000084 GR
 16 C$MotorControl.c$64$3$249                                   =  000087 GR
 16 C$MotorControl.c$66$2$244                                   =  00008B GR
 16 C$MotorControl.c$68$3$250                                   =  000096 GR
 16 C$MotorControl.c$70$3$250                                   =  000099 GR
 16 C$MotorControl.c$71$3$250                                   =  0000A2 GR
 16 C$MotorControl.c$92$2$244                                   =  0000A5 GR
 16 C$MotorControl.c$94$2$244                                   =  0000A6 GR
 16 C$MotorControl.c$95$2$244                                   =  0000A6 GR
 16 C$MotorControl.c$97$3$251                                   =  0000AA GR
 16 C$MotorControl.c$99$2$244                                   =  0000AE GR
    CPRL2                                                       =  0000C8 L
    CT2                                                         =  0000C9 L
    CY                                                          =  0000D7 L
    DPH                                                         =  000083 L
    DPL                                                         =  000082 L
    EA                                                          =  0000AF L
    ES                                                          =  0000AC L
    ET0                                                         =  0000A9 L
    ET1                                                         =  0000AB L
    ET2                                                         =  0000AD L
    EX0                                                         =  0000A8 L
    EX1                                                         =  0000AA L
    EXEN2                                                       =  0000CB L
    EXF2                                                        =  0000CE L
    F0                                                          =  0000D5 L
    G$DRV_OUT$0$0                                               =  0000F8 G
 16 G$MC_Control$0$0                                            =  000000 GR
    G$MOE$0$0                                                   =  0000FF G
  A G$McStaSet$0$0                                              =  000000 GR
  5 G$mcState$0$0                                               =  000000 GR
    IE                                                          =  0000A8 L
    IE.0                                                        =  0000A8 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 7.
Hexadecimal [24-Bits]

Symbol Table

    IE.1                                                        =  0000A9 L
    IE.2                                                        =  0000AA L
    IE.3                                                        =  0000AB L
    IE.4                                                        =  0000AC L
    IE.5                                                        =  0000AD L
    IE.7                                                        =  0000AF L
    IE0                                                         =  000089 L
    IE1                                                         =  00008B L
    INT0                                                        =  0000B2 L
    INT1                                                        =  0000B3 L
    IP                                                          =  0000B8 L
    IP.0                                                        =  0000B8 L
    IP.1                                                        =  0000B9 L
    IP.2                                                        =  0000BA L
    IP.3                                                        =  0000BB L
    IP.4                                                        =  0000BC L
    IP.5                                                        =  0000BD L
    IT0                                                         =  000088 L
    IT1                                                         =  00008A L
    OV                                                          =  0000D2 L
    P                                                           =  0000D0 L
    P0                                                          =  000080 L
    P0.0                                                        =  000080 L
    P0.1                                                        =  000081 L
    P0.2                                                        =  000082 L
    P0.3                                                        =  000083 L
    P0.4                                                        =  000084 L
    P0.5                                                        =  000085 L
    P0.6                                                        =  000086 L
    P0.7                                                        =  000087 L
    P1                                                          =  000090 L
    P1.0                                                        =  000090 L
    P1.1                                                        =  000091 L
    P1.2                                                        =  000092 L
    P1.3                                                        =  000093 L
    P1.4                                                        =  000094 L
    P1.5                                                        =  000095 L
    P1.6                                                        =  000096 L
    P1.7                                                        =  000097 L
    P2                                                          =  0000A0 L
    P2.0                                                        =  0000A0 L
    P2.1                                                        =  0000A1 L
    P2.2                                                        =  0000A2 L
    P2.3                                                        =  0000A3 L
    P2.4                                                        =  0000A4 L
    P2.5                                                        =  0000A5 L
    P2.6                                                        =  0000A6 L
    P2.7                                                        =  0000A7 L
    P3                                                          =  0000B0 L
    P3.0                                                        =  0000B0 L
    P3.1                                                        =  0000B1 L
    P3.2                                                        =  0000B2 L
    P3.3                                                        =  0000B3 L
    P3.4                                                        =  0000B4 L
    P3.5                                                        =  0000B5 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 8.
Hexadecimal [24-Bits]

Symbol Table

    P3.6                                                        =  0000B6 L
    P3.7                                                        =  0000B7 L
    PCON                                                        =  000087 L
    PS                                                          =  0000BC L
    PSW                                                         =  0000D0 L
    PSW.0                                                       =  0000D0 L
    PSW.1                                                       =  0000D1 L
    PSW.2                                                       =  0000D2 L
    PSW.3                                                       =  0000D3 L
    PSW.4                                                       =  0000D4 L
    PSW.5                                                       =  0000D5 L
    PSW.6                                                       =  0000D6 L
    PSW.7                                                       =  0000D7 L
    PT0                                                         =  0000B9 L
    PT1                                                         =  0000BB L
    PT2                                                         =  0000BD L
    PX0                                                         =  0000B8 L
    PX1                                                         =  0000BA L
    RB8                                                         =  00009A L
    RCAP2H                                                      =  0000CB L
    RCAP2L                                                      =  0000CA L
    RCLK                                                        =  0000CD L
    REN                                                         =  00009C L
    RI                                                          =  000098 L
    RS0                                                         =  0000D3 L
    RS1                                                         =  0000D4 L
    RXD                                                         =  0000B0 L
    SBUF                                                        =  000099 L
    SCON                                                        =  000098 L
    SCON.0                                                      =  000098 L
    SCON.1                                                      =  000099 L
    SCON.2                                                      =  00009A L
    SCON.3                                                      =  00009B L
    SCON.4                                                      =  00009C L
    SCON.5                                                      =  00009D L
    SCON.6                                                      =  00009E L
    SCON.7                                                      =  00009F L
    SM0                                                         =  00009F L
    SM1                                                         =  00009E L
    SM2                                                         =  00009D L
    SP                                                          =  000081 L
    T2CON                                                       =  0000C8 L
    T2CON.0                                                     =  0000C8 L
    T2CON.1                                                     =  0000C9 L
    T2CON.2                                                     =  0000CA L
    T2CON.3                                                     =  0000CB L
    T2CON.4                                                     =  0000CC L
    T2CON.5                                                     =  0000CD L
    T2CON.6                                                     =  0000CE L
    T2CON.7                                                     =  0000CF L
    TB8                                                         =  00009B L
    TCLK                                                        =  0000CC L
    TCON                                                        =  000088 L
    TCON.0                                                      =  000088 L
    TCON.1                                                      =  000089 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 9.
Hexadecimal [24-Bits]

Symbol Table

    TCON.2                                                      =  00008A L
    TCON.3                                                      =  00008B L
    TCON.4                                                      =  00008C L
    TCON.5                                                      =  00008D L
    TCON.6                                                      =  00008E L
    TCON.7                                                      =  00008F L
    TF0                                                         =  00008D L
    TF1                                                         =  00008F L
    TF2                                                         =  0000CF L
    TH0                                                         =  00008C L
    TH1                                                         =  00008D L
    TH2                                                         =  0000CD L
    TI                                                          =  000099 L
    TL0                                                         =  00008A L
    TL1                                                         =  00008B L
    TL2                                                         =  0000CC L
    TMOD                                                        =  000089 L
    TR0                                                         =  00008C L
    TR1                                                         =  00008E L
    TR2                                                         =  0000CA L
    TXD                                                         =  0000B1 L
 16 XG$MC_Control$0$0                                           =  000172 GR
    _DRV_OUT                                                    =  0000F8 G
    _FlashData                                                     ****** GX
    _Flash_Erase                                                   ****** GX
    _KS                                                            ****** GX
    _MC_Break                                                      ****** GX
 16 _MC_Control                                                    000000 GR
    _MC_Stop                                                       ****** GX
    _MOE                                                        =  0000FF G
  A _McStaSet                                                      000000 GR
    _Motor_Charge                                                  ****** GX
    _Motor_Init                                                    ****** GX
    _Motor_Ready                                                   ****** GX
    _Motor_Static_Open                                             ****** GX
    _Save_KeyValue                                                 ****** GX
    _UnderProcess                                                  ****** GX
    _fault                                                         ****** GX
    _isCtrlPowOn                                                   ****** GX
    _mcCurOffset                                                   ****** GX
    _mcFaultSource                                                 ****** GX
    _mcFocCtrl                                                     ****** GX
  5 _mcState                                                       000000 GR
    a                                                           =  0000E0 L
    a.0                                                         =  0000E0 L
    a.1                                                         =  0000E1 L
    a.2                                                         =  0000E2 L
    a.3                                                         =  0000E3 L
    a.4                                                         =  0000E4 L
    a.5                                                         =  0000E5 L
    a.6                                                         =  0000E6 L
    a.7                                                         =  0000E7 L
    ac                                                          =  0000D6 L
    acc                                                         =  0000E0 L
    acc.0                                                       =  0000E0 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 10.
Hexadecimal [24-Bits]

Symbol Table

    acc.1                                                       =  0000E1 L
    acc.2                                                       =  0000E2 L
    acc.3                                                       =  0000E3 L
    acc.4                                                       =  0000E4 L
    acc.5                                                       =  0000E5 L
    acc.6                                                       =  0000E6 L
    acc.7                                                       =  0000E7 L
    ar0                                                         =  000000 
    ar1                                                         =  000001 
    ar2                                                         =  000002 
    ar3                                                         =  000003 
    ar4                                                         =  000004 
    ar5                                                         =  000005 
    ar6                                                         =  000006 
    ar7                                                         =  000007 
    b                                                           =  0000F0 L
    b.0                                                         =  0000F0 L
    b.1                                                         =  0000F1 L
    b.2                                                         =  0000F2 L
    b.3                                                         =  0000F3 L
    b.4                                                         =  0000F4 L
    b.5                                                         =  0000F5 L
    b.6                                                         =  0000F6 L
    b.7                                                         =  0000F7 L
    cprl2                                                       =  0000C8 L
    ct2                                                         =  0000C9 L
    cy                                                          =  0000D7 L
    dph                                                         =  000083 L
    dpl                                                         =  000082 L
    ea                                                          =  0000AF L
    es                                                          =  0000AC L
    et0                                                         =  0000A9 L
    et1                                                         =  0000AB L
    et2                                                         =  0000AD L
    ex0                                                         =  0000A8 L
    ex1                                                         =  0000AA L
    exen2                                                       =  0000CB L
    exf2                                                        =  0000CE L
    f0                                                          =  0000D5 L
    ie                                                          =  0000A8 L
    ie.0                                                        =  0000A8 L
    ie.1                                                        =  0000A9 L
    ie.2                                                        =  0000AA L
    ie.3                                                        =  0000AB L
    ie.4                                                        =  0000AC L
    ie.5                                                        =  0000AD L
    ie.7                                                        =  0000AF L
    ie0                                                         =  000089 L
    ie1                                                         =  00008B L
    int0                                                        =  0000B2 L
    int1                                                        =  0000B3 L
    ip                                                          =  0000B8 L
    ip.0                                                        =  0000B8 L
    ip.1                                                        =  0000B9 L
    ip.2                                                        =  0000BA L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 11.
Hexadecimal [24-Bits]

Symbol Table

    ip.3                                                        =  0000BB L
    ip.4                                                        =  0000BC L
    ip.5                                                        =  0000BD L
    it0                                                         =  000088 L
    it1                                                         =  00008A L
    ov                                                          =  0000D2 L
    p                                                           =  0000D0 L
    p0                                                          =  000080 L
    p0.0                                                        =  000080 L
    p0.1                                                        =  000081 L
    p0.2                                                        =  000082 L
    p0.3                                                        =  000083 L
    p0.4                                                        =  000084 L
    p0.5                                                        =  000085 L
    p0.6                                                        =  000086 L
    p0.7                                                        =  000087 L
    p1                                                          =  000090 L
    p1.0                                                        =  000090 L
    p1.1                                                        =  000091 L
    p1.2                                                        =  000092 L
    p1.3                                                        =  000093 L
    p1.4                                                        =  000094 L
    p1.5                                                        =  000095 L
    p1.6                                                        =  000096 L
    p1.7                                                        =  000097 L
    p2                                                          =  0000A0 L
    p2.0                                                        =  0000A0 L
    p2.1                                                        =  0000A1 L
    p2.2                                                        =  0000A2 L
    p2.3                                                        =  0000A3 L
    p2.4                                                        =  0000A4 L
    p2.5                                                        =  0000A5 L
    p2.6                                                        =  0000A6 L
    p2.7                                                        =  0000A7 L
    p3                                                          =  0000B0 L
    p3.0                                                        =  0000B0 L
    p3.1                                                        =  0000B1 L
    p3.2                                                        =  0000B2 L
    p3.3                                                        =  0000B3 L
    p3.4                                                        =  0000B4 L
    p3.5                                                        =  0000B5 L
    p3.6                                                        =  0000B6 L
    p3.7                                                        =  0000B7 L
    pcon                                                        =  000087 L
    ps                                                          =  0000BC L
    psw                                                         =  0000D0 L
    psw.0                                                       =  0000D0 L
    psw.1                                                       =  0000D1 L
    psw.2                                                       =  0000D2 L
    psw.3                                                       =  0000D3 L
    psw.4                                                       =  0000D4 L
    psw.5                                                       =  0000D5 L
    psw.6                                                       =  0000D6 L
    psw.7                                                       =  0000D7 L
    pt0                                                         =  0000B9 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 12.
Hexadecimal [24-Bits]

Symbol Table

    pt1                                                         =  0000BB L
    pt2                                                         =  0000BD L
    px0                                                         =  0000B8 L
    px1                                                         =  0000BA L
    rb8                                                         =  00009A L
    rcap2h                                                      =  0000CB L
    rcap2l                                                      =  0000CA L
    rclk                                                        =  0000CD L
    ren                                                         =  00009C L
    ri                                                          =  000098 L
    rs0                                                         =  0000D3 L
    rs1                                                         =  0000D4 L
    rxd                                                         =  0000B0 L
    sbuf                                                        =  000099 L
    scon                                                        =  000098 L
    scon.0                                                      =  000098 L
    scon.1                                                      =  000099 L
    scon.2                                                      =  00009A L
    scon.3                                                      =  00009B L
    scon.4                                                      =  00009C L
    scon.5                                                      =  00009D L
    scon.6                                                      =  00009E L
    scon.7                                                      =  00009F L
    sm0                                                         =  00009F L
    sm1                                                         =  00009E L
    sm2                                                         =  00009D L
    sp                                                          =  000081 L
    t2con                                                       =  0000C8 L
    t2con.0                                                     =  0000C8 L
    t2con.1                                                     =  0000C9 L
    t2con.2                                                     =  0000CA L
    t2con.3                                                     =  0000CB L
    t2con.4                                                     =  0000CC L
    t2con.5                                                     =  0000CD L
    t2con.6                                                     =  0000CE L
    t2con.7                                                     =  0000CF L
    tb8                                                         =  00009B L
    tclk                                                        =  0000CC L
    tcon                                                        =  000088 L
    tcon.0                                                      =  000088 L
    tcon.1                                                      =  000089 L
    tcon.2                                                      =  00008A L
    tcon.3                                                      =  00008B L
    tcon.4                                                      =  00008C L
    tcon.5                                                      =  00008D L
    tcon.6                                                      =  00008E L
    tcon.7                                                      =  00008F L
    tf0                                                         =  00008D L
    tf1                                                         =  00008F L
    tf2                                                         =  0000CF L
    th0                                                         =  00008C L
    th1                                                         =  00008D L
    th2                                                         =  0000CD L
    ti                                                          =  000099 L
    tl0                                                         =  00008A L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 13.
Hexadecimal [24-Bits]

Symbol Table

    tl1                                                         =  00008B L
    tl2                                                         =  0000CC L
    tmod                                                        =  000089 L
    tr0                                                         =  00008C L
    tr1                                                         =  00008E L
    tr2                                                         =  0000CA L
    txd                                                         =  0000B1 L


ASxxxx Assembler V1.0.2.1  (Intel 8051), page 14.
Hexadecimal [24-Bits]

Area Table

   0 _CODE                                      size      0   flags    0
   1 RSEG                                       size      0   flags    8
   2 RSEG0                                      size      0   flags    8
   3 RSEG1                                      size      0   flags    8
   4 REG_BANK_0                                 size      8   flags    4
   5 DSEG                                       size      1   flags    0
   6 ISEG                                       size      0   flags    0
   7 IABS                                       size      0   flags    8
   8 BSEG                                       size      0   flags   80
   9 PSEG                                       size      0   flags   50
   A XSEG                                       size      1   flags   40
   B XABS                                       size      0   flags   48
   C XISEG                                      size      0   flags   40
   D HOME                                       size      0   flags   20
   E GSINIT0                                    size      0   flags   20
   F GSINIT1                                    size      0   flags   20
  10 GSINIT2                                    size      0   flags   20
  11 GSINIT3                                    size      0   flags   20
  12 GSINIT4                                    size      0   flags   20
  13 GSINIT5                                    size      0   flags   20
  14 GSINIT                                     size      0   flags   20
  15 GSFINAL                                    size      0   flags   20
  16 CSEG                                       size    173   flags   20
  17 CONST                                      size      0   flags   20
  18 XINIT                                      size      0   flags   20
  19 CABS                                       size      0   flags   28

