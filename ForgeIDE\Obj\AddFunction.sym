ASxxxx Assembler V1.0.2.1  (Intel 8051), page 1.
Hexadecimal [24-Bits]

Symbol Table

    .__.$$$.                                                    =  002710 L
    .__.ABS.                                                    =  000000 G
    .__.CPU.                                                    =  000000 L
    .__.H$L.                                                    =  000001 L
    A                                                           =  0000E0 L
 16 A$AddFunction$1000                                             00048A GR
 16 A$AddFunction$1001                                             00048D GR
 16 A$AddFunction$1002                                             000490 GR
 16 A$AddFunction$1003                                             000493 GR
 16 A$AddFunction$1007                                             000496 GR
 16 A$AddFunction$1008                                             000499 GR
 16 A$AddFunction$1009                                             00049C GR
 16 A$AddFunction$1010                                             00049F GR
 16 A$AddFunction$1011                                             0004A1 GR
 16 A$AddFunction$1012                                             0004A4 GR
 16 A$AddFunction$1013                                             0004A5 GR
 16 A$AddFunction$1018                                             0004A7 GR
 16 A$AddFunction$1031                                             0004A8 GR
 16 A$AddFunction$1032                                             0004AA GR
 16 A$AddFunction$1033                                             0004AD GR
 16 A$AddFunction$1035                                             0004AF GR
 16 A$AddFunction$1039                                             0004B0 GR
 16 A$AddFunction$1040                                             0004B3 GR
 16 A$AddFunction$1041                                             0004B4 GR
 16 A$AddFunction$1042                                             0004B5 GR
 16 A$AddFunction$1043                                             0004B6 GR
 16 A$AddFunction$1044                                             0004B7 GR
 16 A$AddFunction$1045                                             0004B8 GR
 16 A$AddFunction$1046                                             0004BB GR
 16 A$AddFunction$1049                                             0004BE GR
 16 A$AddFunction$1050                                             0004C1 GR
 16 A$AddFunction$1051                                             0004C3 GR
 16 A$AddFunction$1052                                             0004C4 GR
 16 A$AddFunction$1053                                             0004C6 GR
 16 A$AddFunction$1054                                             0004C7 GR
 16 A$AddFunction$1057                                             0004C8 GR
 16 A$AddFunction$1058                                             0004C9 GR
 16 A$AddFunction$1059                                             0004CA GR
 16 A$AddFunction$1060                                             0004CB GR
 16 A$AddFunction$1061                                             0004CD GR
 16 A$AddFunction$1062                                             0004CE GR
 16 A$AddFunction$1063                                             0004CF GR
 16 A$AddFunction$1067                                             0004D0 GR
 16 A$AddFunction$1068                                             0004D3 GR
 16 A$AddFunction$1071                                             0004D6 GR
 16 A$AddFunction$1072                                             0004D9 GR
 16 A$AddFunction$1073                                             0004DB GR
 16 A$AddFunction$1074                                             0004DC GR
 16 A$AddFunction$1075                                             0004DE GR
 16 A$AddFunction$1076                                             0004DF GR
 16 A$AddFunction$1079                                             0004E0 GR
 16 A$AddFunction$1080                                             0004E1 GR
 16 A$AddFunction$1081                                             0004E2 GR
 16 A$AddFunction$1082                                             0004E3 GR
 16 A$AddFunction$1083                                             0004E5 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 2.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1084                                             0004E6 GR
 16 A$AddFunction$1085                                             0004E7 GR
 16 A$AddFunction$1089                                             0004E8 GR
 16 A$AddFunction$1090                                             0004EB GR
 16 A$AddFunction$1093                                             0004EE GR
 16 A$AddFunction$1094                                             0004F1 GR
 16 A$AddFunction$1095                                             0004F3 GR
 16 A$AddFunction$1096                                             0004F4 GR
 16 A$AddFunction$1097                                             0004F6 GR
 16 A$AddFunction$1098                                             0004F7 GR
 16 A$AddFunction$1101                                             0004F8 GR
 16 A$AddFunction$1102                                             0004F9 GR
 16 A$AddFunction$1103                                             0004FA GR
 16 A$AddFunction$1104                                             0004FB GR
 16 A$AddFunction$1105                                             0004FD GR
 16 A$AddFunction$1106                                             0004FE GR
 16 A$AddFunction$1107                                             0004FF GR
 16 A$AddFunction$1111                                             000500 GR
 16 A$AddFunction$1112                                             000501 GR
 16 A$AddFunction$1113                                             000503 GR
 16 A$AddFunction$1114                                             000504 GR
 16 A$AddFunction$1115                                             000505 GR
 16 A$AddFunction$1116                                             000506 GR
 16 A$AddFunction$1117                                             000508 GR
 16 A$AddFunction$1118                                             00050B GR
 16 A$AddFunction$1119                                             00050C GR
 16 A$AddFunction$1122                                             00050E GR
 16 A$AddFunction$1123                                             000511 GR
 16 A$AddFunction$1124                                             000513 GR
 16 A$AddFunction$1125                                             000514 GR
 16 A$AddFunction$1126                                             000516 GR
 16 A$AddFunction$1127                                             000517 GR
 16 A$AddFunction$1130                                             000518 GR
 16 A$AddFunction$1131                                             000519 GR
 16 A$AddFunction$1132                                             00051A GR
 16 A$AddFunction$1133                                             00051B GR
 16 A$AddFunction$1134                                             00051D GR
 16 A$AddFunction$1135                                             00051E GR
 16 A$AddFunction$1138                                             00051F GR
 16 A$AddFunction$1139                                             000522 GR
 16 A$AddFunction$1140                                             000524 GR
 16 A$AddFunction$1146                                             000525 GR
 16 A$AddFunction$1159                                             000526 GR
 16 A$AddFunction$1160                                             000527 GR
 16 A$AddFunction$1161                                             000529 GR
 16 A$AddFunction$1162                                             00052B GR
 16 A$AddFunction$1163                                             00052C GR
 16 A$AddFunction$1164                                             00052E GR
 16 A$AddFunction$1167                                             000530 GR
 16 A$AddFunction$1168                                             000533 GR
 16 A$AddFunction$1169                                             000534 GR
 16 A$AddFunction$1170                                             000536 GR
 16 A$AddFunction$1171                                             000537 GR
 16 A$AddFunction$1175                                             000538 GR
 16 A$AddFunction$1176                                             00053A GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 3.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1177                                             00053D GR
 16 A$AddFunction$1179                                             000540 GR
 16 A$AddFunction$1180                                             000542 GR
 16 A$AddFunction$1181                                             000544 GR
 16 A$AddFunction$1185                                             000547 GR
 16 A$AddFunction$1186                                             00054A GR
 16 A$AddFunction$1187                                             00054B GR
 16 A$AddFunction$1188                                             00054C GR
 16 A$AddFunction$1189                                             00054D GR
 16 A$AddFunction$1190                                             00054E GR
 16 A$AddFunction$1191                                             00054F GR
 16 A$AddFunction$1192                                             000552 GR
 16 A$AddFunction$1193                                             000553 GR
 16 A$AddFunction$1194                                             000554 GR
 16 A$AddFunction$1195                                             000555 GR
 16 A$AddFunction$1196                                             000556 GR
 16 A$AddFunction$1197                                             000557 GR
 16 A$AddFunction$1198                                             00055A GR
 16 A$AddFunction$1199                                             00055B GR
 16 A$AddFunction$1200                                             00055C GR
 16 A$AddFunction$1201                                             00055D GR
 16 A$AddFunction$1202                                             00055E GR
 16 A$AddFunction$1203                                             00055F GR
 16 A$AddFunction$1204                                             000562 GR
 16 A$AddFunction$1205                                             000564 GR
 16 A$AddFunction$1206                                             000565 GR
 16 A$AddFunction$1207                                             000567 GR
 16 A$AddFunction$1208                                             000568 GR
 16 A$AddFunction$1209                                             00056B GR
 16 A$AddFunction$1210                                             00056D GR
 16 A$AddFunction$1211                                             000570 GR
 16 A$AddFunction$1212                                             000571 GR
 16 A$AddFunction$1213                                             000572 GR
 16 A$AddFunction$1214                                             000573 GR
 16 A$AddFunction$1217                                             000574 GR
 16 A$AddFunction$1218                                             000577 GR
 16 A$AddFunction$1219                                             000578 GR
 16 A$AddFunction$1220                                             000579 GR
 16 A$AddFunction$1221                                             00057A GR
 16 A$AddFunction$1222                                             00057B GR
 16 A$AddFunction$1223                                             00057C GR
 16 A$AddFunction$1224                                             00057D GR
 16 A$AddFunction$1225                                             00057E GR
 16 A$AddFunction$1226                                             00057F GR
 16 A$AddFunction$1227                                             000580 GR
 16 A$AddFunction$1228                                             000581 GR
 16 A$AddFunction$1229                                             000582 GR
 16 A$AddFunction$1230                                             000585 GR
 16 A$AddFunction$1231                                             000586 GR
 16 A$AddFunction$1232                                             000587 GR
 16 A$AddFunction$1233                                             000588 GR
 16 A$AddFunction$1234                                             000589 GR
 16 A$AddFunction$1235                                             00058A GR
 16 A$AddFunction$1236                                             00058C GR
 16 A$AddFunction$1237                                             00058D GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 4.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1238                                             000590 GR
 16 A$AddFunction$1239                                             000591 GR
 16 A$AddFunction$1240                                             000593 GR
 16 A$AddFunction$1241                                             000596 GR
 16 A$AddFunction$1242                                             000597 GR
 16 A$AddFunction$1243                                             000598 GR
 16 A$AddFunction$1244                                             000599 GR
 16 A$AddFunction$1247                                             00059A GR
 16 A$AddFunction$1248                                             00059C GR
 16 A$AddFunction$1251                                             00059F GR
 16 A$AddFunction$1252                                             0005A2 GR
 16 A$AddFunction$1253                                             0005A3 GR
 16 A$AddFunction$1254                                             0005A4 GR
 16 A$AddFunction$1255                                             0005A5 GR
 16 A$AddFunction$1256                                             0005A6 GR
 16 A$AddFunction$1257                                             0005A7 GR
 16 A$AddFunction$1258                                             0005A8 GR
 16 A$AddFunction$1259                                             0005A9 GR
 16 A$AddFunction$1260                                             0005AA GR
 16 A$AddFunction$1261                                             0005AC GR
 16 A$AddFunction$1262                                             0005AD GR
 16 A$AddFunction$1263                                             0005AE GR
 16 A$AddFunction$1264                                             0005AF GR
 16 A$AddFunction$1265                                             0005B0 GR
 16 A$AddFunction$1266                                             0005B1 GR
 16 A$AddFunction$1267                                             0005B2 GR
 16 A$AddFunction$1268                                             0005B4 GR
 16 A$AddFunction$1269                                             0005B5 GR
 16 A$AddFunction$1270                                             0005B6 GR
 16 A$AddFunction$1271                                             0005B9 GR
 16 A$AddFunction$1272                                             0005BA GR
 16 A$AddFunction$1273                                             0005BB GR
 16 A$AddFunction$1274                                             0005BC GR
 16 A$AddFunction$1277                                             0005BD GR
 16 A$AddFunction$1278                                             0005C0 GR
 16 A$AddFunction$1279                                             0005C1 GR
 16 A$AddFunction$1280                                             0005C2 GR
 16 A$AddFunction$1281                                             0005C3 GR
 16 A$AddFunction$1282                                             0005C4 GR
 16 A$AddFunction$1283                                             0005C5 GR
 16 A$AddFunction$1284                                             0005C6 GR
 16 A$AddFunction$1285                                             0005C7 GR
 16 A$AddFunction$1286                                             0005C8 GR
 16 A$AddFunction$1287                                             0005C9 GR
 16 A$AddFunction$1288                                             0005CA GR
 16 A$AddFunction$1289                                             0005CB GR
 14 A$AddFunction$129                                              000000 GR
 16 A$AddFunction$1290                                             0005CE GR
 16 A$AddFunction$1291                                             0005CF GR
 16 A$AddFunction$1292                                             0005D0 GR
 16 A$AddFunction$1293                                             0005D1 GR
 16 A$AddFunction$1294                                             0005D2 GR
 16 A$AddFunction$1295                                             0005D3 GR
 16 A$AddFunction$1296                                             0005D6 GR
 16 A$AddFunction$1297                                             0005D8 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 5.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1298                                             0005D9 GR
 16 A$AddFunction$1299                                             0005DB GR
 14 A$AddFunction$130                                              000003 GR
 16 A$AddFunction$1300                                             0005DC GR
 16 A$AddFunction$1301                                             0005DF GR
 16 A$AddFunction$1302                                             0005E0 GR
 16 A$AddFunction$1303                                             0005E2 GR
 16 A$AddFunction$1304                                             0005E5 GR
 16 A$AddFunction$1305                                             0005E6 GR
 16 A$AddFunction$1306                                             0005E7 GR
 16 A$AddFunction$1307                                             0005E8 GR
 16 A$AddFunction$1308                                             0005E9 GR
 14 A$AddFunction$131                                              000004 GR
 16 A$AddFunction$1312                                             0005EB GR
 16 A$AddFunction$1313                                             0005EE GR
 16 A$AddFunction$1314                                             0005EF GR
 16 A$AddFunction$1315                                             0005F0 GR
 16 A$AddFunction$1316                                             0005F1 GR
 16 A$AddFunction$1319                                             0005F2 GR
 14 A$AddFunction$132                                              000005 GR
 16 A$AddFunction$1320                                             0005F5 GR
 16 A$AddFunction$1321                                             0005F6 GR
 16 A$AddFunction$1322                                             0005F7 GR
 16 A$AddFunction$1326                                             0005F8 GR
 16 A$AddFunction$1327                                             0005F9 GR
 16 A$AddFunction$1328                                             0005FB GR
 16 A$AddFunction$1329                                             0005FD GR
 14 A$AddFunction$133                                              000006 GR
 16 A$AddFunction$1330                                             0005FE GR
 16 A$AddFunction$1331                                             000600 GR
 16 A$AddFunction$1334                                             000602 GR
 16 A$AddFunction$1335                                             000605 GR
 16 A$AddFunction$1336                                             000606 GR
 16 A$AddFunction$1337                                             000607 GR
 16 A$AddFunction$1338                                             000608 GR
 16 A$AddFunction$1339                                             000609 GR
 16 A$AddFunction$1340                                             00060A GR
 16 A$AddFunction$1341                                             00060D GR
 16 A$AddFunction$1342                                             00060E GR
 16 A$AddFunction$1343                                             00060F GR
 16 A$AddFunction$1344                                             000610 GR
 16 A$AddFunction$1345                                             000611 GR
 16 A$AddFunction$1346                                             000612 GR
 16 A$AddFunction$1347                                             000615 GR
 16 A$AddFunction$1348                                             000616 GR
 16 A$AddFunction$1349                                             000617 GR
 16 A$AddFunction$1350                                             000618 GR
 16 A$AddFunction$1351                                             000619 GR
 16 A$AddFunction$1352                                             00061A GR
 16 A$AddFunction$1353                                             00061D GR
 16 A$AddFunction$1354                                             00061F GR
 16 A$AddFunction$1355                                             000620 GR
 16 A$AddFunction$1356                                             000622 GR
 16 A$AddFunction$1357                                             000623 GR
 16 A$AddFunction$1358                                             000626 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 6.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1359                                             000627 GR
 14 A$AddFunction$136                                              000007 GR
 16 A$AddFunction$1360                                             000629 GR
 16 A$AddFunction$1361                                             00062C GR
 16 A$AddFunction$1362                                             00062D GR
 16 A$AddFunction$1363                                             00062E GR
 16 A$AddFunction$1364                                             00062F GR
 16 A$AddFunction$1368                                             000630 GR
 16 A$AddFunction$1369                                             000633 GR
 14 A$AddFunction$137                                              00000A GR
 16 A$AddFunction$1370                                             000634 GR
 16 A$AddFunction$1371                                             000635 GR
 16 A$AddFunction$1372                                             000638 GR
 16 A$AddFunction$1373                                             00063A GR
 16 A$AddFunction$1374                                             00063D GR
 16 A$AddFunction$1375                                             00063F GR
 16 A$AddFunction$1378                                             000641 GR
 16 A$AddFunction$1379                                             000643 GR
 16 A$AddFunction$1380                                             000645 GR
 16 A$AddFunction$1381                                             000646 GR
 16 A$AddFunction$1382                                             000647 GR
 16 A$AddFunction$1383                                             000649 GR
 16 A$AddFunction$1384                                             00064A GR
 16 A$AddFunction$1385                                             00064C GR
 16 A$AddFunction$1388                                             00064E GR
 16 A$AddFunction$1389                                             00064F GR
 16 A$AddFunction$1390                                             000652 GR
 16 A$AddFunction$1392                                             000653 GR
 16 A$AddFunction$1393                                             000655 GR
 16 A$AddFunction$1394                                             000657 GR
 16 A$AddFunction$1398                                             000659 GR
 16 A$AddFunction$1399                                             00065C GR
 14 A$AddFunction$140                                              00000B GR
 16 A$AddFunction$1400                                             00065F GR
 16 A$AddFunction$1404                                             000661 GR
 16 A$AddFunction$1405                                             000664 GR
 16 A$AddFunction$1409                                             000667 GR
 16 A$AddFunction$1412                                             00066A GR
 16 A$AddFunction$1413                                             00066D GR
 16 A$AddFunction$1414                                             00066E GR
 16 A$AddFunction$1415                                             00066F GR
 16 A$AddFunction$1416                                             000670 GR
 16 A$AddFunction$1417                                             000671 GR
 16 A$AddFunction$1420                                             000672 GR
 16 A$AddFunction$1421                                             000675 GR
 16 A$AddFunction$1422                                             000676 GR
 16 A$AddFunction$1423                                             000677 GR
 16 A$AddFunction$1424                                             000678 GR
 16 A$AddFunction$1425                                             000679 GR
 16 A$AddFunction$1426                                             00067A GR
 16 A$AddFunction$1427                                             00067D GR
 16 A$AddFunction$1428                                             00067E GR
 16 A$AddFunction$1429                                             00067F GR
 16 A$AddFunction$1430                                             000680 GR
 16 A$AddFunction$1431                                             000681 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 7.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1432                                             000682 GR
 16 A$AddFunction$1433                                             000685 GR
 16 A$AddFunction$1434                                             000687 GR
 16 A$AddFunction$1435                                             000688 GR
 16 A$AddFunction$1436                                             00068A GR
 16 A$AddFunction$1437                                             00068B GR
 16 A$AddFunction$1438                                             00068E GR
 16 A$AddFunction$1439                                             000690 GR
 16 A$AddFunction$1440                                             000693 GR
 16 A$AddFunction$1441                                             000694 GR
 16 A$AddFunction$1442                                             000695 GR
 16 A$AddFunction$1443                                             000696 GR
 16 A$AddFunction$1446                                             000697 GR
 16 A$AddFunction$1447                                             00069A GR
 16 A$AddFunction$1448                                             00069B GR
 16 A$AddFunction$1449                                             00069C GR
 16 A$AddFunction$1450                                             00069D GR
 16 A$AddFunction$1451                                             00069E GR
 16 A$AddFunction$1452                                             00069F GR
 16 A$AddFunction$1453                                             0006A0 GR
 16 A$AddFunction$1454                                             0006A1 GR
 16 A$AddFunction$1455                                             0006A2 GR
 16 A$AddFunction$1456                                             0006A3 GR
 16 A$AddFunction$1457                                             0006A4 GR
 16 A$AddFunction$1458                                             0006A5 GR
 16 A$AddFunction$1459                                             0006A8 GR
 16 A$AddFunction$1460                                             0006A9 GR
 16 A$AddFunction$1461                                             0006AA GR
 16 A$AddFunction$1462                                             0006AB GR
 16 A$AddFunction$1463                                             0006AC GR
 16 A$AddFunction$1464                                             0006AD GR
 16 A$AddFunction$1465                                             0006B0 GR
 16 A$AddFunction$1466                                             0006B2 GR
 16 A$AddFunction$1467                                             0006B3 GR
 16 A$AddFunction$1468                                             0006B5 GR
 16 A$AddFunction$1469                                             0006B6 GR
 16 A$AddFunction$1470                                             0006B9 GR
 16 A$AddFunction$1471                                             0006BB GR
 16 A$AddFunction$1472                                             0006BE GR
 16 A$AddFunction$1473                                             0006BF GR
 16 A$AddFunction$1474                                             0006C0 GR
 16 A$AddFunction$1475                                             0006C1 GR
 16 A$AddFunction$1478                                             0006C2 GR
 16 A$AddFunction$1479                                             0006C5 GR
 16 A$AddFunction$1480                                             0006C6 GR
 16 A$AddFunction$1481                                             0006C7 GR
 16 A$AddFunction$1482                                             0006C8 GR
 16 A$AddFunction$1483                                             0006C9 GR
 16 A$AddFunction$1484                                             0006CA GR
 16 A$AddFunction$1485                                             0006CD GR
 16 A$AddFunction$1486                                             0006CE GR
 16 A$AddFunction$1487                                             0006CF GR
 16 A$AddFunction$1488                                             0006D0 GR
 16 A$AddFunction$1489                                             0006D1 GR
 16 A$AddFunction$1490                                             0006D2 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 8.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1491                                             0006D5 GR
 16 A$AddFunction$1492                                             0006D6 GR
 16 A$AddFunction$1493                                             0006D7 GR
 16 A$AddFunction$1494                                             0006D8 GR
 16 A$AddFunction$1495                                             0006D9 GR
 16 A$AddFunction$1496                                             0006DA GR
 16 A$AddFunction$1497                                             0006DD GR
 16 A$AddFunction$1498                                             0006DF GR
 16 A$AddFunction$1499                                             0006E0 GR
 16 A$AddFunction$1500                                             0006E2 GR
 16 A$AddFunction$1501                                             0006E3 GR
 16 A$AddFunction$1502                                             0006E6 GR
 16 A$AddFunction$1503                                             0006E8 GR
 16 A$AddFunction$1504                                             0006EB GR
 16 A$AddFunction$1505                                             0006EC GR
 16 A$AddFunction$1506                                             0006ED GR
 16 A$AddFunction$1507                                             0006EE GR
 16 A$AddFunction$1510                                             0006EF GR
 16 A$AddFunction$1511                                             0006F2 GR
 16 A$AddFunction$1512                                             0006F3 GR
 16 A$AddFunction$1513                                             0006F4 GR
 16 A$AddFunction$1514                                             0006F5 GR
 16 A$AddFunction$1515                                             0006F6 GR
 16 A$AddFunction$1516                                             0006F7 GR
 16 A$AddFunction$1517                                             0006FA GR
 16 A$AddFunction$1518                                             0006FB GR
 16 A$AddFunction$1519                                             0006FC GR
 16 A$AddFunction$1520                                             0006FD GR
 16 A$AddFunction$1521                                             0006FE GR
 16 A$AddFunction$1522                                             0006FF GR
 16 A$AddFunction$1523                                             000702 GR
 16 A$AddFunction$1524                                             000703 GR
 16 A$AddFunction$1525                                             000704 GR
 16 A$AddFunction$1526                                             000705 GR
 16 A$AddFunction$1527                                             000706 GR
 16 A$AddFunction$1528                                             000707 GR
 16 A$AddFunction$1529                                             00070A GR
 16 A$AddFunction$1530                                             00070C GR
 16 A$AddFunction$1531                                             00070D GR
 16 A$AddFunction$1532                                             00070F GR
 16 A$AddFunction$1533                                             000710 GR
 16 A$AddFunction$1534                                             000713 GR
 16 A$AddFunction$1535                                             000714 GR
 16 A$AddFunction$1536                                             000716 GR
 16 A$AddFunction$1537                                             000719 GR
 16 A$AddFunction$1538                                             00071A GR
 16 A$AddFunction$1539                                             00071B GR
 16 A$AddFunction$1540                                             00071C GR
 16 A$AddFunction$1543                                             00071D GR
 16 A$AddFunction$1546                                             000720 GR
 16 A$AddFunction$1549                                             000723 GR
 16 A$AddFunction$1552                                             000726 GR
 16 A$AddFunction$1555                                             000729 GR
 16 A$AddFunction$1556                                             00072C GR
 16 A$AddFunction$1557                                             00072D GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 9.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1560                                             00072F GR
 16 A$AddFunction$1561                                             000730 GR
 16 A$AddFunction$1562                                             000731 GR
 16 A$AddFunction$1563                                             000732 GR
 16 A$AddFunction$1564                                             000733 GR
 16 A$AddFunction$1565                                             000734 GR
 16 A$AddFunction$1566                                             000735 GR
 16 A$AddFunction$1567                                             000736 GR
 16 A$AddFunction$1568                                             000739 GR
 16 A$AddFunction$1570                                             00073A GR
 16 A$AddFunction$1571                                             00073D GR
 16 A$AddFunction$1572                                             00073E GR
 16 A$AddFunction$1573                                             00073F GR
 16 A$AddFunction$1574                                             000740 GR
 16 A$AddFunction$1575                                             000741 GR
 16 A$AddFunction$1578                                             000742 GR
 16 A$AddFunction$1579                                             000745 GR
 16 A$AddFunction$1580                                             000746 GR
 16 A$AddFunction$1581                                             000747 GR
 16 A$AddFunction$1582                                             000748 GR
 16 A$AddFunction$1583                                             000749 GR
 16 A$AddFunction$1584                                             00074A GR
 16 A$AddFunction$1585                                             00074B GR
 16 A$AddFunction$1586                                             00074D GR
 16 A$AddFunction$1587                                             00074E GR
 16 A$AddFunction$1588                                             00074F GR
 16 A$AddFunction$1589                                             000750 GR
 16 A$AddFunction$1592                                             000752 GR
 16 A$AddFunction$1593                                             000755 GR
 16 A$AddFunction$1594                                             000756 GR
 16 A$AddFunction$1597                                             000757 GR
 16 A$AddFunction$1598                                             000758 GR
 16 A$AddFunction$1599                                             000759 GR
 16 A$AddFunction$1600                                             00075A GR
 16 A$AddFunction$1604                                             00075B GR
 16 A$AddFunction$1607                                             00075E GR
 16 A$AddFunction$1608                                             000761 GR
 16 A$AddFunction$1609                                             000762 GR
 16 A$AddFunction$1610                                             000763 GR
 16 A$AddFunction$1611                                             000764 GR
 16 A$AddFunction$1612                                             000765 GR
 16 A$AddFunction$1613                                             000766 GR
 16 A$AddFunction$1614                                             000767 GR
 16 A$AddFunction$1617                                             000769 GR
 16 A$AddFunction$1618                                             00076A GR
 16 A$AddFunction$1619                                             00076D GR
 16 A$AddFunction$1621                                             00076E GR
 16 A$AddFunction$1622                                             000771 GR
 16 A$AddFunction$1623                                             000772 GR
 16 A$AddFunction$1624                                             000773 GR
 16 A$AddFunction$1625                                             000774 GR
 16 A$AddFunction$1626                                             000775 GR
 16 A$AddFunction$1630                                             000776 GR
 16 A$AddFunction$1631                                             000778 GR
 16 A$AddFunction$1632                                             00077B GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 10.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1633                                             00077E GR
 16 A$AddFunction$1634                                             00077F GR
 16 A$AddFunction$1635                                             000780 GR
 16 A$AddFunction$1636                                             000781 GR
 16 A$AddFunction$1637                                             000782 GR
 16 A$AddFunction$1638                                             000783 GR
 16 A$AddFunction$1639                                             000784 GR
 16 A$AddFunction$1642                                             000786 GR
 16 A$AddFunction$1643                                             000787 GR
 16 A$AddFunction$1644                                             00078A GR
 16 A$AddFunction$1646                                             00078B GR
 16 A$AddFunction$1647                                             00078E GR
 16 A$AddFunction$1648                                             00078F GR
 16 A$AddFunction$1649                                             000790 GR
 16 A$AddFunction$1650                                             000791 GR
 16 A$AddFunction$1651                                             000792 GR
 16 A$AddFunction$1657                                             000793 GR
 16 A$AddFunction$1670                                             000794 GR
 16 A$AddFunction$1671                                             000797 GR
 16 A$AddFunction$1672                                             000798 GR
 16 A$AddFunction$1673                                             000799 GR
 16 A$AddFunction$1676                                             00079C GR
 16 A$AddFunction$1677                                             00079D GR
 16 A$AddFunction$1678                                             00079E GR
 16 A$AddFunction$1679                                             00079F GR
 16 A$AddFunction$1680                                             0007A0 GR
 16 A$AddFunction$1681                                             0007A1 GR
 16 A$AddFunction$1682                                             0007A2 GR
 16 A$AddFunction$1683                                             0007A3 GR
 16 A$AddFunction$1684                                             0007A4 GR
 16 A$AddFunction$1685                                             0007A7 GR
 16 A$AddFunction$1687                                             0007A8 GR
 16 A$AddFunction$1688                                             0007AB GR
 16 A$AddFunction$1689                                             0007AC GR
 16 A$AddFunction$1690                                             0007AD GR
 16 A$AddFunction$1691                                             0007AE GR
 16 A$AddFunction$1692                                             0007AF GR
 16 A$AddFunction$1695                                             0007B0 GR
 16 A$AddFunction$1696                                             0007B3 GR
 16 A$AddFunction$1697                                             0007B4 GR
 16 A$AddFunction$1698                                             0007B5 GR
 16 A$AddFunction$1699                                             0007B6 GR
 16 A$AddFunction$170                                              000000 GR
 16 A$AddFunction$1700                                             0007B7 GR
 16 A$AddFunction$1701                                             0007B8 GR
 16 A$AddFunction$1702                                             0007B9 GR
 16 A$AddFunction$1703                                             0007BB GR
 16 A$AddFunction$1704                                             0007BC GR
 16 A$AddFunction$1705                                             0007BD GR
 16 A$AddFunction$1706                                             0007BE GR
 16 A$AddFunction$1709                                             0007C0 GR
 16 A$AddFunction$1710                                             0007C3 GR
 16 A$AddFunction$1711                                             0007C4 GR
 16 A$AddFunction$1712                                             0007C5 GR
 16 A$AddFunction$1713                                             0007C6 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 11.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$1716                                             0007C7 GR
 16 A$AddFunction$1717                                             0007CA GR
 16 A$AddFunction$1718                                             0007CC GR
 16 A$AddFunction$1719                                             0007CD GR
 16 A$AddFunction$1723                                             0007CE GR
 16 A$AddFunction$1726                                             0007D1 GR
 16 A$AddFunction$1727                                             0007D4 GR
 16 A$AddFunction$1728                                             0007D5 GR
 16 A$AddFunction$1729                                             0007D6 GR
 16 A$AddFunction$1730                                             0007D7 GR
 16 A$AddFunction$1731                                             0007D8 GR
 16 A$AddFunction$1732                                             0007D9 GR
 16 A$AddFunction$1733                                             0007DA GR
 16 A$AddFunction$1734                                             0007DD GR
 16 A$AddFunction$1736                                             0007DE GR
 16 A$AddFunction$1737                                             0007E1 GR
 16 A$AddFunction$1738                                             0007E2 GR
 16 A$AddFunction$1739                                             0007E3 GR
 16 A$AddFunction$1740                                             0007E4 GR
 16 A$AddFunction$1741                                             0007E5 GR
 16 A$AddFunction$1744                                             0007E6 GR
 16 A$AddFunction$1745                                             0007E9 GR
 16 A$AddFunction$1746                                             0007EA GR
 16 A$AddFunction$1747                                             0007EB GR
 16 A$AddFunction$1748                                             0007EC GR
 16 A$AddFunction$1749                                             0007ED GR
 16 A$AddFunction$1750                                             0007EE GR
 16 A$AddFunction$1751                                             0007EF GR
 16 A$AddFunction$1752                                             0007F1 GR
 16 A$AddFunction$1753                                             0007F2 GR
 16 A$AddFunction$1754                                             0007F3 GR
 16 A$AddFunction$1755                                             0007F4 GR
 16 A$AddFunction$1758                                             0007F6 GR
 16 A$AddFunction$1759                                             0007F9 GR
 16 A$AddFunction$1760                                             0007FA GR
 16 A$AddFunction$1761                                             0007FB GR
 16 A$AddFunction$1762                                             0007FC GR
 16 A$AddFunction$1765                                             0007FD GR
 16 A$AddFunction$1766                                             000800 GR
 16 A$AddFunction$1767                                             000801 GR
 16 A$AddFunction$1773                                             000802 GR
 16 A$AddFunction$183                                              000001 GR
 16 A$AddFunction$184                                              000004 GR
 16 A$AddFunction$185                                              000006 GR
 16 A$AddFunction$186                                              000007 GR
 16 A$AddFunction$187                                              000009 GR
 16 A$AddFunction$188                                              00000A GR
 16 A$AddFunction$191                                              00000B GR
 16 A$AddFunction$194                                              00000D GR
 16 A$AddFunction$195                                              00000F GR
 16 A$AddFunction$196                                              000011 GR
 16 A$AddFunction$197                                              000012 GR
 16 A$AddFunction$198                                              000013 GR
 16 A$AddFunction$199                                              000015 GR
 16 A$AddFunction$200                                              000016 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 12.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$201                                              000018 GR
 16 A$AddFunction$202                                              00001A GR
 16 A$AddFunction$206                                              00001D GR
 16 A$AddFunction$207                                              00001F GR
 16 A$AddFunction$208                                              000020 GR
 16 A$AddFunction$209                                              000021 GR
 16 A$AddFunction$210                                              000022 GR
 16 A$AddFunction$211                                              000024 GR
 16 A$AddFunction$212                                              000025 GR
 16 A$AddFunction$213                                              000026 GR
 16 A$AddFunction$214                                              000028 GR
 16 A$AddFunction$215                                              00002B GR
 16 A$AddFunction$216                                              00002C GR
 16 A$AddFunction$217                                              00002E GR
 16 A$AddFunction$218                                              000030 GR
 16 A$AddFunction$219                                              000032 GR
 16 A$AddFunction$220                                              000034 GR
 16 A$AddFunction$221                                              000036 GR
 16 A$AddFunction$222                                              000038 GR
 16 A$AddFunction$223                                              00003A GR
 16 A$AddFunction$224                                              00003D GR
 16 A$AddFunction$225                                              000040 GR
 16 A$AddFunction$226                                              000042 GR
 16 A$AddFunction$227                                              000045 GR
 16 A$AddFunction$228                                              000046 GR
 16 A$AddFunction$229                                              000048 GR
 16 A$AddFunction$230                                              00004A GR
 16 A$AddFunction$231                                              00004C GR
 16 A$AddFunction$232                                              00004E GR
 16 A$AddFunction$233                                              000050 GR
 16 A$AddFunction$234                                              000052 GR
 16 A$AddFunction$235                                              000054 GR
 16 A$AddFunction$236                                              000056 GR
 16 A$AddFunction$237                                              000058 GR
 16 A$AddFunction$238                                              00005A GR
 16 A$AddFunction$239                                              00005D GR
 16 A$AddFunction$240                                              000060 GR
 16 A$AddFunction$241                                              000062 GR
 16 A$AddFunction$242                                              000065 GR
 16 A$AddFunction$243                                              000066 GR
 16 A$AddFunction$244                                              000068 GR
 16 A$AddFunction$245                                              00006A GR
 16 A$AddFunction$246                                              00006C GR
 16 A$AddFunction$247                                              00006E GR
 16 A$AddFunction$248                                              000070 GR
 16 A$AddFunction$249                                              000072 GR
 16 A$AddFunction$250                                              000073 GR
 16 A$AddFunction$251                                              000076 GR
 16 A$AddFunction$252                                              000078 GR
 16 A$AddFunction$253                                              00007B GR
 16 A$AddFunction$254                                              00007C GR
 16 A$AddFunction$255                                              00007D GR
 16 A$AddFunction$256                                              00007E GR
 16 A$AddFunction$259                                              00007F GR
 16 A$AddFunction$260                                              000082 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 13.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$261                                              000083 GR
 16 A$AddFunction$262                                              000084 GR
 16 A$AddFunction$263                                              000085 GR
 16 A$AddFunction$264                                              000086 GR
 16 A$AddFunction$265                                              000087 GR
 16 A$AddFunction$266                                              000088 GR
 16 A$AddFunction$267                                              00008A GR
 16 A$AddFunction$268                                              00008B GR
 16 A$AddFunction$269                                              00008D GR
 16 A$AddFunction$270                                              00008F GR
 16 A$AddFunction$273                                              000091 GR
 16 A$AddFunction$274                                              000094 GR
 16 A$AddFunction$275                                              000096 GR
 16 A$AddFunction$276                                              000097 GR
 16 A$AddFunction$277                                              000099 GR
 16 A$AddFunction$278                                              00009A GR
 16 A$AddFunction$279                                              00009B GR
 16 A$AddFunction$283                                              00009C GR
 16 A$AddFunction$284                                              00009F GR
 16 A$AddFunction$285                                              0000A1 GR
 16 A$AddFunction$286                                              0000A2 GR
 16 A$AddFunction$287                                              0000A4 GR
 16 A$AddFunction$288                                              0000A5 GR
 16 A$AddFunction$294                                              0000A6 GR
 16 A$AddFunction$309                                              0000A7 GR
 16 A$AddFunction$310                                              0000A9 GR
 16 A$AddFunction$311                                              0000AC GR
 16 A$AddFunction$313                                              0000AE GR
 16 A$AddFunction$314                                              0000B0 GR
 16 A$AddFunction$315                                              0000B3 GR
 16 A$AddFunction$317                                              0000B5 GR
 16 A$AddFunction$322                                              0000B6 GR
 16 A$AddFunction$323                                              0000B9 GR
 16 A$AddFunction$324                                              0000BA GR
 16 A$AddFunction$325                                              0000BB GR
 16 A$AddFunction$326                                              0000BD GR
 16 A$AddFunction$327                                              0000C0 GR
 16 A$AddFunction$329                                              0000C3 GR
 16 A$AddFunction$335                                              0000C4 GR
 16 A$AddFunction$336                                              0000C7 GR
 16 A$AddFunction$337                                              0000C8 GR
 16 A$AddFunction$338                                              0000C9 GR
 16 A$AddFunction$339                                              0000CA GR
 16 A$AddFunction$340                                              0000CB GR
 16 A$AddFunction$341                                              0000CC GR
 16 A$AddFunction$342                                              0000CD GR
 16 A$AddFunction$343                                              0000CF GR
 16 A$AddFunction$344                                              0000D0 GR
 16 A$AddFunction$345                                              0000D2 GR
 16 A$AddFunction$346                                              0000D4 GR
 16 A$AddFunction$347                                              0000D7 GR
 16 A$AddFunction$348                                              0000D9 GR
 16 A$AddFunction$349                                              0000DB GR
 16 A$AddFunction$353                                              0000DE GR
 16 A$AddFunction$354                                              0000E1 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 14.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$355                                              0000E2 GR
 16 A$AddFunction$356                                              0000E3 GR
 16 A$AddFunction$359                                              0000E4 GR
 16 A$AddFunction$360                                              0000E7 GR
 16 A$AddFunction$361                                              0000E8 GR
 16 A$AddFunction$362                                              0000E9 GR
 16 A$AddFunction$363                                              0000EA GR
 16 A$AddFunction$364                                              0000EC GR
 16 A$AddFunction$365                                              0000EE GR
 16 A$AddFunction$369                                              0000EF GR
 16 A$AddFunction$370                                              0000F2 GR
 16 A$AddFunction$371                                              0000F4 GR
 16 A$AddFunction$372                                              0000F5 GR
 16 A$AddFunction$373                                              0000F7 GR
 16 A$AddFunction$374                                              0000F8 GR
 16 A$AddFunction$377                                              0000F9 GR
 16 A$AddFunction$378                                              0000FA GR
 16 A$AddFunction$379                                              0000FC GR
 16 A$AddFunction$380                                              0000FD GR
 16 A$AddFunction$381                                              0000FF GR
 16 A$AddFunction$382                                              000100 GR
 16 A$AddFunction$385                                              000101 GR
 16 A$AddFunction$386                                              000104 GR
 16 A$AddFunction$387                                              000106 GR
 16 A$AddFunction$388                                              000107 GR
 16 A$AddFunction$389                                              000109 GR
 16 A$AddFunction$390                                              00010A GR
 16 A$AddFunction$393                                              00010B GR
 16 A$AddFunction$394                                              00010E GR
 16 A$AddFunction$395                                              000110 GR
 16 A$AddFunction$396                                              000111 GR
 16 A$AddFunction$397                                              000113 GR
 16 A$AddFunction$398                                              000114 GR
 16 A$AddFunction$401                                              000115 GR
 16 A$AddFunction$402                                              000118 GR
 16 A$AddFunction$403                                              00011A GR
 16 A$AddFunction$404                                              00011B GR
 16 A$AddFunction$405                                              00011D GR
 16 A$AddFunction$406                                              00011E GR
 16 A$AddFunction$409                                              00011F GR
 16 A$AddFunction$410                                              000122 GR
 16 A$AddFunction$411                                              000123 GR
 16 A$AddFunction$412                                              000124 GR
 16 A$AddFunction$415                                              000127 GR
 16 A$AddFunction$416                                              00012A GR
 16 A$AddFunction$417                                              00012B GR
 16 A$AddFunction$418                                              00012C GR
 16 A$AddFunction$419                                              00012D GR
 16 A$AddFunction$420                                              00012E GR
 16 A$AddFunction$421                                              00012F GR
 16 A$AddFunction$422                                              000131 GR
 16 A$AddFunction$423                                              000132 GR
 16 A$AddFunction$424                                              000135 GR
 16 A$AddFunction$425                                              000136 GR
 16 A$AddFunction$426                                              000138 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 15.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$427                                              00013A GR
 16 A$AddFunction$428                                              00013C GR
 16 A$AddFunction$429                                              00013E GR
 16 A$AddFunction$430                                              000140 GR
 16 A$AddFunction$431                                              000142 GR
 16 A$AddFunction$432                                              000144 GR
 16 A$AddFunction$436                                              000146 GR
 16 A$AddFunction$437                                              000149 GR
 16 A$AddFunction$438                                              00014A GR
 16 A$AddFunction$439                                              00014B GR
 16 A$AddFunction$440                                              00014C GR
 16 A$AddFunction$441                                              00014D GR
 16 A$AddFunction$442                                              00014E GR
 16 A$AddFunction$443                                              000150 GR
 16 A$AddFunction$444                                              000151 GR
 16 A$AddFunction$445                                              000154 GR
 16 A$AddFunction$446                                              000155 GR
 16 A$AddFunction$447                                              000157 GR
 16 A$AddFunction$448                                              000159 GR
 16 A$AddFunction$449                                              00015B GR
 16 A$AddFunction$450                                              00015D GR
 16 A$AddFunction$451                                              00015F GR
 16 A$AddFunction$452                                              000161 GR
 16 A$AddFunction$456                                              000163 GR
 16 A$AddFunction$457                                              000166 GR
 16 A$AddFunction$458                                              000168 GR
 16 A$AddFunction$461                                              000169 GR
 16 A$AddFunction$462                                              00016C GR
 16 A$AddFunction$463                                              00016F GR
 16 A$AddFunction$464                                              000172 GR
 16 A$AddFunction$467                                              000175 GR
 16 A$AddFunction$468                                              000178 GR
 16 A$AddFunction$469                                              00017B GR
 16 A$AddFunction$470                                              00017E GR
 16 A$AddFunction$473                                              000181 GR
 16 A$AddFunction$474                                              000184 GR
 16 A$AddFunction$475                                              000185 GR
 16 A$AddFunction$476                                              000186 GR
 16 A$AddFunction$477                                              000187 GR
 16 A$AddFunction$478                                              000188 GR
 16 A$AddFunction$479                                              000189 GR
 16 A$AddFunction$480                                              00018C GR
 16 A$AddFunction$481                                              00018D GR
 16 A$AddFunction$482                                              00018E GR
 16 A$AddFunction$483                                              00018F GR
 16 A$AddFunction$484                                              000190 GR
 16 A$AddFunction$487                                              000191 GR
 16 A$AddFunction$488                                              000194 GR
 16 A$AddFunction$489                                              000195 GR
 16 A$AddFunction$490                                              000196 GR
 16 A$AddFunction$491                                              000197 GR
 16 A$AddFunction$494                                              000198 GR
 16 A$AddFunction$495                                              00019B GR
 16 A$AddFunction$496                                              00019C GR
 16 A$AddFunction$497                                              00019D GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 16.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$498                                              00019E GR
 16 A$AddFunction$499                                              00019F GR
 16 A$AddFunction$500                                              0001A0 GR
 16 A$AddFunction$501                                              0001A3 GR
 16 A$AddFunction$502                                              0001A4 GR
 16 A$AddFunction$503                                              0001A5 GR
 16 A$AddFunction$504                                              0001A6 GR
 16 A$AddFunction$505                                              0001A7 GR
 16 A$AddFunction$508                                              0001A8 GR
 16 A$AddFunction$511                                              0001AB GR
 16 A$AddFunction$514                                              0001AE GR
 16 A$AddFunction$515                                              0001B1 GR
 16 A$AddFunction$518                                              0001B4 GR
 16 A$AddFunction$519                                              0001B7 GR
 16 A$AddFunction$522                                              0001BA GR
 16 A$AddFunction$523                                              0001BD GR
 16 A$AddFunction$526                                              0001C0 GR
 16 A$AddFunction$527                                              0001C3 GR
 16 A$AddFunction$530                                              0001C6 GR
 16 A$AddFunction$531                                              0001C9 GR
 16 A$AddFunction$534                                              0001CC GR
 16 A$AddFunction$535                                              0001CF GR
 16 A$AddFunction$536                                              0001D1 GR
 16 A$AddFunction$537                                              0001D2 GR
 16 A$AddFunction$541                                              0001D3 GR
 16 A$AddFunction$542                                              0001D6 GR
 16 A$AddFunction$543                                              0001D7 GR
 16 A$AddFunction$546                                              0001D8 GR
 16 A$AddFunction$552                                              0001D9 GR
 16 A$AddFunction$553                                              0001DC GR
 16 A$AddFunction$554                                              0001DD GR
 16 A$AddFunction$555                                              0001DE GR
 16 A$AddFunction$558                                              0001DF GR
 16 A$AddFunction$559                                              0001E2 GR
 16 A$AddFunction$560                                              0001E3 GR
 16 A$AddFunction$561                                              0001E4 GR
 16 A$AddFunction$562                                              0001E5 GR
 16 A$AddFunction$564                                              0001E8 GR
 16 A$AddFunction$565                                              0001EA GR
 16 A$AddFunction$569                                              0001EB GR
 16 A$AddFunction$570                                              0001EE GR
 16 A$AddFunction$571                                              0001EF GR
 16 A$AddFunction$574                                              0001F0 GR
 16 A$AddFunction$575                                              0001F3 GR
 16 A$AddFunction$576                                              0001F4 GR
 16 A$AddFunction$577                                              0001F5 GR
 16 A$AddFunction$578                                              0001F6 GR
 16 A$AddFunction$579                                              0001F7 GR
 16 A$AddFunction$580                                              0001F8 GR
 16 A$AddFunction$581                                              0001FA GR
 16 A$AddFunction$582                                              0001FB GR
 16 A$AddFunction$583                                              0001FE GR
 16 A$AddFunction$584                                              0001FF GR
 16 A$AddFunction$585                                              000201 GR
 16 A$AddFunction$586                                              000204 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 17.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$587                                              000205 GR
 16 A$AddFunction$588                                              000206 GR
 16 A$AddFunction$589                                              000207 GR
 16 A$AddFunction$592                                              000208 GR
 16 A$AddFunction$593                                              00020B GR
 16 A$AddFunction$594                                              00020C GR
 16 A$AddFunction$595                                              00020D GR
 16 A$AddFunction$596                                              00020E GR
 16 A$AddFunction$597                                              00020F GR
 16 A$AddFunction$598                                              000210 GR
 16 A$AddFunction$599                                              000211 GR
 16 A$AddFunction$600                                              000212 GR
 16 A$AddFunction$601                                              000213 GR
 16 A$AddFunction$602                                              000214 GR
 16 A$AddFunction$603                                              000215 GR
 16 A$AddFunction$604                                              000216 GR
 16 A$AddFunction$605                                              000217 GR
 16 A$AddFunction$606                                              000219 GR
 16 A$AddFunction$607                                              00021C GR
 16 A$AddFunction$608                                              00021E GR
 16 A$AddFunction$609                                              000221 GR
 16 A$AddFunction$610                                              000222 GR
 16 A$AddFunction$611                                              000223 GR
 16 A$AddFunction$612                                              000224 GR
 16 A$AddFunction$615                                              000225 GR
 16 A$AddFunction$616                                              000228 GR
 16 A$AddFunction$617                                              000229 GR
 16 A$AddFunction$618                                              00022A GR
 16 A$AddFunction$619                                              00022B GR
 16 A$AddFunction$620                                              00022C GR
 16 A$AddFunction$621                                              00022D GR
 16 A$AddFunction$622                                              000230 GR
 16 A$AddFunction$623                                              000231 GR
 16 A$AddFunction$624                                              000232 GR
 16 A$AddFunction$625                                              000233 GR
 16 A$AddFunction$626                                              000234 GR
 16 A$AddFunction$627                                              000235 GR
 16 A$AddFunction$628                                              000236 GR
 16 A$AddFunction$629                                              000237 GR
 16 A$AddFunction$630                                              000238 GR
 16 A$AddFunction$631                                              000239 GR
 16 A$AddFunction$632                                              00023A GR
 16 A$AddFunction$633                                              00023C GR
 16 A$AddFunction$634                                              00023F GR
 16 A$AddFunction$635                                              000241 GR
 16 A$AddFunction$636                                              000244 GR
 16 A$AddFunction$637                                              000245 GR
 16 A$AddFunction$638                                              000246 GR
 16 A$AddFunction$639                                              000247 GR
 16 A$AddFunction$642                                              000248 GR
 16 A$AddFunction$643                                              00024B GR
 16 A$AddFunction$644                                              00024C GR
 16 A$AddFunction$645                                              00024D GR
 16 A$AddFunction$646                                              00024E GR
 16 A$AddFunction$647                                              00024F GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 18.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$648                                              000250 GR
 16 A$AddFunction$649                                              000253 GR
 16 A$AddFunction$650                                              000254 GR
 16 A$AddFunction$651                                              000255 GR
 16 A$AddFunction$652                                              000256 GR
 16 A$AddFunction$653                                              000257 GR
 16 A$AddFunction$654                                              000258 GR
 16 A$AddFunction$655                                              000259 GR
 16 A$AddFunction$656                                              00025A GR
 16 A$AddFunction$657                                              00025B GR
 16 A$AddFunction$658                                              00025D GR
 16 A$AddFunction$659                                              00025F GR
 16 A$AddFunction$660                                              000262 GR
 16 A$AddFunction$661                                              000264 GR
 16 A$AddFunction$664                                              000266 GR
 16 A$AddFunction$665                                              000269 GR
 16 A$AddFunction$666                                              00026A GR
 16 A$AddFunction$667                                              00026B GR
 16 A$AddFunction$668                                              00026C GR
 16 A$AddFunction$669                                              00026D GR
 16 A$AddFunction$670                                              00026E GR
 16 A$AddFunction$671                                              00026F GR
 16 A$AddFunction$672                                              000270 GR
 16 A$AddFunction$673                                              000271 GR
 16 A$AddFunction$674                                              000272 GR
 16 A$AddFunction$675                                              000273 GR
 16 A$AddFunction$676                                              000274 GR
 16 A$AddFunction$677                                              000275 GR
 16 A$AddFunction$678                                              000277 GR
 16 A$AddFunction$679                                              000279 GR
 16 A$AddFunction$680                                              00027B GR
 16 A$AddFunction$681                                              00027D GR
 16 A$AddFunction$682                                              00027E GR
 16 A$AddFunction$683                                              000281 GR
 16 A$AddFunction$684                                              000283 GR
 16 A$AddFunction$685                                              000285 GR
 16 A$AddFunction$686                                              000287 GR
 16 A$AddFunction$687                                              00028A GR
 16 A$AddFunction$688                                              00028B GR
 16 A$AddFunction$689                                              00028C GR
 16 A$AddFunction$690                                              00028D GR
 16 A$AddFunction$693                                              00028E GR
 16 A$AddFunction$694                                              000291 GR
 16 A$AddFunction$695                                              000292 GR
 16 A$AddFunction$696                                              000293 GR
 16 A$AddFunction$697                                              000294 GR
 16 A$AddFunction$698                                              000295 GR
 16 A$AddFunction$699                                              000296 GR
 16 A$AddFunction$700                                              000299 GR
 16 A$AddFunction$701                                              00029A GR
 16 A$AddFunction$702                                              00029B GR
 16 A$AddFunction$703                                              00029C GR
 16 A$AddFunction$704                                              00029D GR
 16 A$AddFunction$705                                              00029E GR
 16 A$AddFunction$706                                              00029F GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 19.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$707                                              0002A0 GR
 16 A$AddFunction$708                                              0002A1 GR
 16 A$AddFunction$709                                              0002A2 GR
 16 A$AddFunction$710                                              0002A3 GR
 16 A$AddFunction$711                                              0002A6 GR
 16 A$AddFunction$712                                              0002A7 GR
 16 A$AddFunction$713                                              0002A8 GR
 16 A$AddFunction$714                                              0002A9 GR
 16 A$AddFunction$715                                              0002AA GR
 16 A$AddFunction$716                                              0002AB GR
 16 A$AddFunction$720                                              0002AD GR
 16 A$AddFunction$721                                              0002B0 GR
 16 A$AddFunction$722                                              0002B1 GR
 16 A$AddFunction$723                                              0002B2 GR
 16 A$AddFunction$724                                              0002B3 GR
 16 A$AddFunction$725                                              0002B4 GR
 16 A$AddFunction$729                                              0002B5 GR
 16 A$AddFunction$730                                              0002B8 GR
 16 A$AddFunction$731                                              0002B9 GR
 16 A$AddFunction$732                                              0002BA GR
 16 A$AddFunction$733                                              0002BB GR
 16 A$AddFunction$734                                              0002BC GR
 16 A$AddFunction$735                                              0002BD GR
 16 A$AddFunction$736                                              0002C0 GR
 16 A$AddFunction$737                                              0002C1 GR
 16 A$AddFunction$738                                              0002C2 GR
 16 A$AddFunction$739                                              0002C3 GR
 16 A$AddFunction$740                                              0002C4 GR
 16 A$AddFunction$743                                              0002C5 GR
 16 A$AddFunction$744                                              0002C8 GR
 16 A$AddFunction$745                                              0002C9 GR
 16 A$AddFunction$746                                              0002CA GR
 16 A$AddFunction$747                                              0002CB GR
 16 A$AddFunction$748                                              0002CC GR
 16 A$AddFunction$749                                              0002CD GR
 16 A$AddFunction$750                                              0002CE GR
 16 A$AddFunction$751                                              0002D0 GR
 16 A$AddFunction$752                                              0002D1 GR
 16 A$AddFunction$753                                              0002D3 GR
 16 A$AddFunction$754                                              0002D5 GR
 16 A$AddFunction$757                                              0002D7 GR
 16 A$AddFunction$758                                              0002DA GR
 16 A$AddFunction$759                                              0002DB GR
 16 A$AddFunction$760                                              0002DC GR
 16 A$AddFunction$761                                              0002DD GR
 16 A$AddFunction$762                                              0002DE GR
 16 A$AddFunction$763                                              0002DF GR
 16 A$AddFunction$764                                              0002E0 GR
 16 A$AddFunction$765                                              0002E3 GR
 16 A$AddFunction$767                                              0002E4 GR
 16 A$AddFunction$768                                              0002E7 GR
 16 A$AddFunction$769                                              0002E8 GR
 16 A$AddFunction$770                                              0002E9 GR
 16 A$AddFunction$771                                              0002EA GR
 16 A$AddFunction$772                                              0002EB GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 20.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$780                                              0002EC GR
 16 A$AddFunction$793                                              0002ED GR
 16 A$AddFunction$794                                              0002F0 GR
 16 A$AddFunction$795                                              0002F2 GR
 16 A$AddFunction$796                                              0002F3 GR
 16 A$AddFunction$797                                              0002F4 GR
 16 A$AddFunction$798                                              0002F5 GR
 16 A$AddFunction$799                                              0002F6 GR
 16 A$AddFunction$802                                              0002F7 GR
 16 A$AddFunction$803                                              0002FA GR
 16 A$AddFunction$804                                              0002FB GR
 16 A$AddFunction$805                                              0002FD GR
 16 A$AddFunction$806                                              0002FE GR
 16 A$AddFunction$807                                              0002FF GR
 16 A$AddFunction$810                                              000301 GR
 16 A$AddFunction$811                                              000304 GR
 16 A$AddFunction$812                                              000307 GR
 16 A$AddFunction$813                                              00030A GR
 16 A$AddFunction$814                                              00030D GR
 16 A$AddFunction$815                                              000310 GR
 16 A$AddFunction$816                                              000312 GR
 16 A$AddFunction$817                                              000315 GR
 16 A$AddFunction$818                                              000316 GR
 16 A$AddFunction$819                                              000318 GR
 16 A$AddFunction$820                                              00031A GR
 16 A$AddFunction$821                                              00031C GR
 16 A$AddFunction$822                                              00031E GR
 16 A$AddFunction$823                                              000320 GR
 16 A$AddFunction$824                                              000322 GR
 16 A$AddFunction$825                                              000324 GR
 16 A$AddFunction$826                                              000327 GR
 16 A$AddFunction$827                                              00032A GR
 16 A$AddFunction$828                                              00032D GR
 16 A$AddFunction$829                                              00032F GR
 16 A$AddFunction$830                                              000332 GR
 16 A$AddFunction$831                                              000333 GR
 16 A$AddFunction$832                                              000335 GR
 16 A$AddFunction$833                                              000337 GR
 16 A$AddFunction$834                                              000339 GR
 16 A$AddFunction$835                                              00033A GR
 16 A$AddFunction$836                                              00033C GR
 16 A$AddFunction$840                                              00033F GR
 16 A$AddFunction$841                                              000342 GR
 16 A$AddFunction$842                                              000345 GR
 16 A$AddFunction$843                                              000348 GR
 16 A$AddFunction$844                                              00034B GR
 16 A$AddFunction$845                                              00034E GR
 16 A$AddFunction$846                                              000351 GR
 16 A$AddFunction$847                                              000353 GR
 16 A$AddFunction$848                                              000355 GR
 16 A$AddFunction$849                                              000357 GR
 16 A$AddFunction$850                                              000359 GR
 16 A$AddFunction$851                                              00035C GR
 16 A$AddFunction$852                                              00035F GR
 16 A$AddFunction$853                                              000362 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 21.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$854                                              000364 GR
 16 A$AddFunction$855                                              000367 GR
 16 A$AddFunction$856                                              000368 GR
 16 A$AddFunction$857                                              00036A GR
 16 A$AddFunction$858                                              00036C GR
 16 A$AddFunction$859                                              00036E GR
 16 A$AddFunction$860                                              000370 GR
 16 A$AddFunction$861                                              000372 GR
 16 A$AddFunction$862                                              000374 GR
 16 A$AddFunction$863                                              000376 GR
 16 A$AddFunction$864                                              000378 GR
 16 A$AddFunction$865                                              00037A GR
 16 A$AddFunction$866                                              00037C GR
 16 A$AddFunction$867                                              00037F GR
 16 A$AddFunction$868                                              000381 GR
 16 A$AddFunction$869                                              000384 GR
 16 A$AddFunction$870                                              000386 GR
 16 A$AddFunction$871                                              000389 GR
 16 A$AddFunction$872                                              00038C GR
 16 A$AddFunction$873                                              00038F GR
 16 A$AddFunction$874                                              000391 GR
 16 A$AddFunction$875                                              000393 GR
 16 A$AddFunction$876                                              000395 GR
 16 A$AddFunction$877                                              000397 GR
 16 A$AddFunction$878                                              000399 GR
 16 A$AddFunction$879                                              00039B GR
 16 A$AddFunction$880                                              00039D GR
 16 A$AddFunction$881                                              00039F GR
 16 A$AddFunction$882                                              0003A1 GR
 16 A$AddFunction$883                                              0003A3 GR
 16 A$AddFunction$884                                              0003A5 GR
 16 A$AddFunction$885                                              0003A6 GR
 16 A$AddFunction$886                                              0003A9 GR
 16 A$AddFunction$887                                              0003AA GR
 16 A$AddFunction$888                                              0003AC GR
 16 A$AddFunction$889                                              0003AE GR
 16 A$AddFunction$890                                              0003B0 GR
 16 A$AddFunction$891                                              0003B1 GR
 16 A$AddFunction$894                                              0003B3 GR
 16 A$AddFunction$895                                              0003B5 GR
 16 A$AddFunction$896                                              0003B7 GR
 16 A$AddFunction$897                                              0003B9 GR
 16 A$AddFunction$898                                              0003BB GR
 16 A$AddFunction$899                                              0003BE GR
 16 A$AddFunction$900                                              0003C1 GR
 16 A$AddFunction$901                                              0003C4 GR
 16 A$AddFunction$902                                              0003C6 GR
 16 A$AddFunction$903                                              0003C9 GR
 16 A$AddFunction$904                                              0003CA GR
 16 A$AddFunction$905                                              0003CC GR
 16 A$AddFunction$906                                              0003CE GR
 16 A$AddFunction$907                                              0003D0 GR
 16 A$AddFunction$908                                              0003D2 GR
 16 A$AddFunction$909                                              0003D4 GR
 16 A$AddFunction$910                                              0003D6 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 22.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$911                                              0003D8 GR
 16 A$AddFunction$912                                              0003DA GR
 16 A$AddFunction$913                                              0003DC GR
 16 A$AddFunction$914                                              0003DE GR
 16 A$AddFunction$918                                              0003E1 GR
 16 A$AddFunction$919                                              0003E4 GR
 16 A$AddFunction$920                                              0003E7 GR
 16 A$AddFunction$921                                              0003EA GR
 16 A$AddFunction$922                                              0003ED GR
 16 A$AddFunction$926                                              0003F0 GR
 16 A$AddFunction$927                                              0003F3 GR
 16 A$AddFunction$928                                              0003F6 GR
 16 A$AddFunction$929                                              0003F9 GR
 16 A$AddFunction$930                                              0003FC GR
 16 A$AddFunction$931                                              0003FF GR
 16 A$AddFunction$932                                              000402 GR
 16 A$AddFunction$933                                              000404 GR
 16 A$AddFunction$934                                              000406 GR
 16 A$AddFunction$935                                              000408 GR
 16 A$AddFunction$936                                              00040A GR
 16 A$AddFunction$937                                              00040D GR
 16 A$AddFunction$938                                              000410 GR
 16 A$AddFunction$939                                              000413 GR
 16 A$AddFunction$940                                              000415 GR
 16 A$AddFunction$941                                              000418 GR
 16 A$AddFunction$942                                              000419 GR
 16 A$AddFunction$943                                              00041B GR
 16 A$AddFunction$944                                              00041D GR
 16 A$AddFunction$945                                              00041F GR
 16 A$AddFunction$946                                              000421 GR
 16 A$AddFunction$947                                              000423 GR
 16 A$AddFunction$948                                              000425 GR
 16 A$AddFunction$949                                              000427 GR
 16 A$AddFunction$950                                              000429 GR
 16 A$AddFunction$951                                              00042B GR
 16 A$AddFunction$952                                              00042D GR
 16 A$AddFunction$953                                              000430 GR
 16 A$AddFunction$954                                              000432 GR
 16 A$AddFunction$955                                              000435 GR
 16 A$AddFunction$956                                              000437 GR
 16 A$AddFunction$957                                              00043A GR
 16 A$AddFunction$958                                              00043D GR
 16 A$AddFunction$959                                              000440 GR
 16 A$AddFunction$960                                              000442 GR
 16 A$AddFunction$961                                              000444 GR
 16 A$AddFunction$962                                              000446 GR
 16 A$AddFunction$963                                              000448 GR
 16 A$AddFunction$964                                              00044A GR
 16 A$AddFunction$965                                              00044C GR
 16 A$AddFunction$966                                              00044E GR
 16 A$AddFunction$967                                              000450 GR
 16 A$AddFunction$968                                              000453 GR
 16 A$AddFunction$969                                              000454 GR
 16 A$AddFunction$970                                              000456 GR
 16 A$AddFunction$971                                              000458 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 23.
Hexadecimal [24-Bits]

Symbol Table

 16 A$AddFunction$972                                              00045A GR
 16 A$AddFunction$973                                              00045B GR
 16 A$AddFunction$976                                              00045D GR
 16 A$AddFunction$977                                              00045F GR
 16 A$AddFunction$978                                              000461 GR
 16 A$AddFunction$979                                              000463 GR
 16 A$AddFunction$980                                              000465 GR
 16 A$AddFunction$981                                              000468 GR
 16 A$AddFunction$982                                              00046B GR
 16 A$AddFunction$983                                              00046E GR
 16 A$AddFunction$984                                              000470 GR
 16 A$AddFunction$985                                              000473 GR
 16 A$AddFunction$986                                              000474 GR
 16 A$AddFunction$987                                              000476 GR
 16 A$AddFunction$988                                              000478 GR
 16 A$AddFunction$989                                              00047A GR
 16 A$AddFunction$990                                              00047C GR
 16 A$AddFunction$991                                              00047E GR
 16 A$AddFunction$992                                              000480 GR
 16 A$AddFunction$993                                              000482 GR
 16 A$AddFunction$994                                              000484 GR
 16 A$AddFunction$995                                              000486 GR
 16 A$AddFunction$996                                              000488 GR
    A.0                                                         =  0000E0 L
    A.1                                                         =  0000E1 L
    A.2                                                         =  0000E2 L
    A.3                                                         =  0000E3 L
    A.4                                                         =  0000E4 L
    A.5                                                         =  0000E5 L
    A.6                                                         =  0000E6 L
    A.7                                                         =  0000E7 L
    AC                                                          =  0000D6 L
    ACC                                                         =  0000E0 L
    ACC.0                                                       =  0000E0 L
    ACC.1                                                       =  0000E1 L
    ACC.2                                                       =  0000E2 L
    ACC.3                                                       =  0000E3 L
    ACC.4                                                       =  0000E4 L
    ACC.5                                                       =  0000E5 L
    ACC.6                                                       =  0000E6 L
    ACC.7                                                       =  0000E7 L
    B                                                           =  0000F0 L
    B.0                                                         =  0000F0 L
    B.1                                                         =  0000F1 L
    B.2                                                         =  0000F2 L
    B.3                                                         =  0000F3 L
    B.4                                                         =  0000F4 L
    B.5                                                         =  0000F5 L
    B.6                                                         =  0000F6 L
    B.7                                                         =  0000F7 L
 14 C$AddFunction.c$15$1$347                                    =  00000B GR
 16 C$AddFunction.c$215$1$280                                   =  000001 GR
 16 C$AddFunction.c$229$2$280                                   =  000001 GR
 16 C$AddFunction.c$230$2$280                                   =  00000B GR
 16 C$AddFunction.c$238$3$281                                   =  00000D GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 24.
Hexadecimal [24-Bits]

Symbol Table

 16 C$AddFunction.c$240$4$282                                   =  00001D GR
 16 C$AddFunction.c$241$4$282                                   =  00007F GR
 16 C$AddFunction.c$243$5$283                                   =  000091 GR
 16 C$AddFunction.c$248$4$284                                   =  00009C GR
 16 C$AddFunction.c$265$3$280                                   =  0000A6 GR
 16 C$AddFunction.c$27$0$241                                    =  000000 GR
 16 C$AddFunction.c$273$3$286                                   =  0000A7 GR
 14 C$AddFunction.c$275$1$286                                   =  000000 GR
 14 C$AddFunction.c$276$1$286                                   =  000007 GR
 16 C$AddFunction.c$278$1$286                                   =  0000A7 GR
 16 C$AddFunction.c$280$2$287                                   =  0000B6 GR
 16 C$AddFunction.c$282$3$288                                   =  0000C4 GR
 16 C$AddFunction.c$284$4$289                                   =  0000C4 GR
 16 C$AddFunction.c$286$5$290                                   =  0000DE GR
 16 C$AddFunction.c$288$5$290                                   =  0000E4 GR
 16 C$AddFunction.c$291$6$291                                   =  0000EF GR
 16 C$AddFunction.c$292$6$291                                   =  0000F9 GR
 16 C$AddFunction.c$293$6$291                                   =  000101 GR
 16 C$AddFunction.c$294$6$291                                   =  00010B GR
 16 C$AddFunction.c$295$6$291                                   =  000115 GR
 16 C$AddFunction.c$299$7$292                                   =  00011F GR
 16 C$AddFunction.c$301$8$293                                   =  000127 GR
 16 C$AddFunction.c$305$8$294                                   =  000146 GR
 16 C$AddFunction.c$317$6$291                                   =  000163 GR
 16 C$AddFunction.c$318$6$291                                   =  000169 GR
 16 C$AddFunction.c$319$6$291                                   =  000175 GR
 16 C$AddFunction.c$320$6$291                                   =  000181 GR
 16 C$AddFunction.c$321$6$291                                   =  000191 GR
 16 C$AddFunction.c$322$6$291                                   =  000198 GR
 16 C$AddFunction.c$324$6$291                                   =  0001A8 GR
 16 C$AddFunction.c$333$7$295                                   =  0001AB GR
 16 C$AddFunction.c$334$7$295                                   =  0001AE GR
 16 C$AddFunction.c$335$7$295                                   =  0001B4 GR
 16 C$AddFunction.c$336$7$295                                   =  0001BA GR
 16 C$AddFunction.c$337$7$295                                   =  0001C0 GR
 16 C$AddFunction.c$338$7$295                                   =  0001C6 GR
 16 C$AddFunction.c$341$6$291                                   =  0001CC GR
 16 C$AddFunction.c$346$5$296                                   =  0001D3 GR
 16 C$AddFunction.c$349$3$288                                   =  0001D8 GR
 16 C$AddFunction.c$351$3$288                                   =  0001D9 GR
 16 C$AddFunction.c$353$4$297                                   =  0001D9 GR
 16 C$AddFunction.c$355$4$297                                   =  0001DF GR
 16 C$AddFunction.c$357$5$298                                   =  0001EB GR
 16 C$AddFunction.c$358$5$298                                   =  0001F0 GR
 16 C$AddFunction.c$366$6$299                                   =  000208 GR
 16 C$AddFunction.c$367$6$299                                   =  000225 GR
 16 C$AddFunction.c$369$6$299                                   =  000248 GR
 16 C$AddFunction.c$371$7$300                                   =  000266 GR
 16 C$AddFunction.c$372$7$300                                   =  00028E GR
 16 C$AddFunction.c$376$7$301                                   =  0002AD GR
 16 C$AddFunction.c$385$7$302                                   =  0002B5 GR
 16 C$AddFunction.c$405$5$298                                   =  0002C5 GR
 16 C$AddFunction.c$407$6$303                                   =  0002D7 GR
 16 C$AddFunction.c$427$1$286                                   =  0002EC GR
 16 C$AddFunction.c$429$1$286                                   =  0002EC GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 25.
Hexadecimal [24-Bits]

Symbol Table

 16 C$AddFunction.c$439$1$305                                   =  0002ED GR
 16 C$AddFunction.c$441$1$305                                   =  0002F7 GR
 16 C$AddFunction.c$443$1$305                                   =  000301 GR
 16 C$AddFunction.c$445$2$306                                   =  00033F GR
 16 C$AddFunction.c$447$1$305                                   =  0003B3 GR
 16 C$AddFunction.c$451$3$308                                   =  0003E1 GR
 16 C$AddFunction.c$456$2$309                                   =  0003F0 GR
 16 C$AddFunction.c$458$1$305                                   =  00045D GR
 16 C$AddFunction.c$462$3$311                                   =  00048A GR
 16 C$AddFunction.c$466$1$305                                   =  000496 GR
 16 C$AddFunction.c$467$1$305                                   =  0004A7 GR
 16 C$AddFunction.c$473$1$313                                   =  0004A8 GR
 16 C$AddFunction.c$475$1$313                                   =  0004A8 GR
 16 C$AddFunction.c$477$2$314                                   =  0004B0 GR
 16 C$AddFunction.c$479$3$315                                   =  0004BE GR
 16 C$AddFunction.c$480$3$315                                   =  0004C8 GR
 16 C$AddFunction.c$482$2$314                                   =  0004D0 GR
 16 C$AddFunction.c$484$3$316                                   =  0004D6 GR
 16 C$AddFunction.c$485$3$316                                   =  0004E0 GR
 16 C$AddFunction.c$487$2$314                                   =  0004E8 GR
 16 C$AddFunction.c$489$3$317                                   =  0004EE GR
 16 C$AddFunction.c$490$3$317                                   =  0004F8 GR
 16 C$AddFunction.c$492$2$314                                   =  000500 GR
 16 C$AddFunction.c$494$3$318                                   =  00050E GR
 16 C$AddFunction.c$495$3$318                                   =  000518 GR
 16 C$AddFunction.c$496$3$318                                   =  00051F GR
 16 C$AddFunction.c$499$1$313                                   =  000525 GR
 16 C$AddFunction.c$506$1$320                                   =  000526 GR
 16 C$AddFunction.c$512$2$321                                   =  000526 GR
 16 C$AddFunction.c$514$3$322                                   =  000530 GR
 16 C$AddFunction.c$523$1$320                                   =  000538 GR
 16 C$AddFunction.c$526$2$323                                   =  000547 GR
 16 C$AddFunction.c$527$2$323                                   =  000574 GR
 16 C$AddFunction.c$529$2$323                                   =  00059A GR
 16 C$AddFunction.c$531$3$324                                   =  00059F GR
 16 C$AddFunction.c$532$3$324                                   =  0005BD GR
 16 C$AddFunction.c$537$2$325                                   =  0005EB GR
 16 C$AddFunction.c$538$2$325                                   =  0005F2 GR
 16 C$AddFunction.c$544$2$326                                   =  0005F8 GR
 16 C$AddFunction.c$546$3$327                                   =  000602 GR
 16 C$AddFunction.c$549$2$326                                   =  000630 GR
 16 C$AddFunction.c$551$3$328                                   =  000641 GR
 16 C$AddFunction.c$553$4$329                                   =  00064E GR
 16 C$AddFunction.c$557$4$330                                   =  000659 GR
 16 C$AddFunction.c$562$3$331                                   =  000661 GR
 16 C$AddFunction.c$565$2$326                                   =  000667 GR
 16 C$AddFunction.c$573$1$320                                   =  00066A GR
 16 C$AddFunction.c$574$1$320                                   =  000672 GR
 16 C$AddFunction.c$579$2$332                                   =  000697 GR
 16 C$AddFunction.c$582$1$320                                   =  0006C2 GR
 16 C$AddFunction.c$583$1$320                                   =  0006EF GR
 16 C$AddFunction.c$585$1$320                                   =  00071D GR
 16 C$AddFunction.c$587$1$320                                   =  000720 GR
 16 C$AddFunction.c$589$1$320                                   =  000723 GR
 16 C$AddFunction.c$591$1$320                                   =  000726 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 26.
Hexadecimal [24-Bits]

Symbol Table

 16 C$AddFunction.c$594$1$320                                   =  000729 GR
 16 C$AddFunction.c$596$2$333                                   =  00072F GR
 16 C$AddFunction.c$597$2$333                                   =  000742 GR
 16 C$AddFunction.c$599$3$334                                   =  000752 GR
 16 C$AddFunction.c$600$3$334                                   =  000757 GR
 16 C$AddFunction.c$665$2$335                                   =  00075B GR
 16 C$AddFunction.c$670$1$320                                   =  00075E GR
 16 C$AddFunction.c$672$2$336                                   =  000769 GR
 16 C$AddFunction.c$674$1$320                                   =  000776 GR
 16 C$AddFunction.c$676$2$337                                   =  000786 GR
 16 C$AddFunction.c$678$1$320                                   =  000793 GR
 16 C$AddFunction.c$703$1$347                                   =  000794 GR
 16 C$AddFunction.c$705$1$347                                   =  000794 GR
 16 C$AddFunction.c$707$2$348                                   =  00079C GR
 16 C$AddFunction.c$708$2$348                                   =  0007B0 GR
 16 C$AddFunction.c$710$3$349                                   =  0007C0 GR
 16 C$AddFunction.c$711$3$349                                   =  0007C7 GR
 16 C$AddFunction.c$714$1$347                                   =  0007CE GR
 16 C$AddFunction.c$716$2$350                                   =  0007D1 GR
 16 C$AddFunction.c$717$2$350                                   =  0007E6 GR
 16 C$AddFunction.c$719$3$351                                   =  0007F6 GR
 16 C$AddFunction.c$720$3$351                                   =  0007FD GR
 16 C$AddFunction.c$723$1$347                                   =  000802 GR
    CPRL2                                                       =  0000C8 L
    CT2                                                         =  0000C9 L
    CY                                                          =  0000D7 L
    DPH                                                         =  000083 L
    DPL                                                         =  000082 L
    EA                                                          =  0000AF L
    ES                                                          =  0000AC L
    ET0                                                         =  0000A9 L
    ET1                                                         =  0000AB L
    ET2                                                         =  0000AD L
    EX0                                                         =  0000A8 L
    EX1                                                         =  0000AA L
    EXEN2                                                       =  0000CB L
    EXF2                                                        =  0000CE L
    F0                                                          =  0000D5 L
 16 G$ATORamp$0$0                                               =  0004A8 GR
 16 G$Abs_F16$0$0                                               =  000000 GR
 16 G$Motor_Ramp$0$0                                            =  0002ED GR
 16 G$Speed_response$0$0                                        =  0000A7 GR
 16 G$TargetRef_Process$0$0                                     =  000001 GR
 16 G$TickCycle_1ms$0$0                                         =  000526 GR
  8 G$isCtrlPowOn$0$0                                           =  000000 GR
  A G$mcFocCtrl$0$0                                             =  000011 GR
  A G$mcPwmInput$0$0                                            =  000000 GR
  5 G$mcRefRamp$0$0                                             =  000000 GR
 16 G$zeroLoss$0$0                                              =  000794 GR
    IE                                                          =  0000A8 L
    IE.0                                                        =  0000A8 L
    IE.1                                                        =  0000A9 L
    IE.2                                                        =  0000AA L
    IE.3                                                        =  0000AB L
    IE.4                                                        =  0000AC L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 27.
Hexadecimal [24-Bits]

Symbol Table

    IE.5                                                        =  0000AD L
    IE.7                                                        =  0000AF L
    IE0                                                         =  000089 L
    IE1                                                         =  00008B L
    INT0                                                        =  0000B2 L
    INT1                                                        =  0000B3 L
    IP                                                          =  0000B8 L
    IP.0                                                        =  0000B8 L
    IP.1                                                        =  0000B9 L
    IP.2                                                        =  0000BA L
    IP.3                                                        =  0000BB L
    IP.4                                                        =  0000BC L
    IP.5                                                        =  0000BD L
    IT0                                                         =  000088 L
    IT1                                                         =  00008A L
  A LAddFunction.Motor_Ramp$ref$1$304                           =  00005C GR
  5 LAddFunction.Motor_Ramp$sloc0$1$0                           =  00000E GR
  5 LAddFunction.Motor_Ramp$sloc1$1$0                           =  000012 GR
  5 LAddFunction.Motor_Ramp$sloc2$1$0                           =  000014 GR
  5 LAddFunction.Motor_Ramp$sloc3$1$0                           =  000018 GR
  A LAddFunction.Speed_response$F5SEG_Flag$1$286                =  00005B GR
  A LAddFunction.Speed_response$refRampOut$1$286                =  000059 GR
    OV                                                          =  0000D2 L
    P                                                           =  0000D0 L
    P0                                                          =  000080 L
    P0.0                                                        =  000080 L
    P0.1                                                        =  000081 L
    P0.2                                                        =  000082 L
    P0.3                                                        =  000083 L
    P0.4                                                        =  000084 L
    P0.5                                                        =  000085 L
    P0.6                                                        =  000086 L
    P0.7                                                        =  000087 L
    P1                                                          =  000090 L
    P1.0                                                        =  000090 L
    P1.1                                                        =  000091 L
    P1.2                                                        =  000092 L
    P1.3                                                        =  000093 L
    P1.4                                                        =  000094 L
    P1.5                                                        =  000095 L
    P1.6                                                        =  000096 L
    P1.7                                                        =  000097 L
    P2                                                          =  0000A0 L
    P2.0                                                        =  0000A0 L
    P2.1                                                        =  0000A1 L
    P2.2                                                        =  0000A2 L
    P2.3                                                        =  0000A3 L
    P2.4                                                        =  0000A4 L
    P2.5                                                        =  0000A5 L
    P2.6                                                        =  0000A6 L
    P2.7                                                        =  0000A7 L
    P3                                                          =  0000B0 L
    P3.0                                                        =  0000B0 L
    P3.1                                                        =  0000B1 L
    P3.2                                                        =  0000B2 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 28.
Hexadecimal [24-Bits]

Symbol Table

    P3.3                                                        =  0000B3 L
    P3.4                                                        =  0000B4 L
    P3.5                                                        =  0000B5 L
    P3.6                                                        =  0000B6 L
    P3.7                                                        =  0000B7 L
    PCON                                                        =  000087 L
    PS                                                          =  0000BC L
    PSW                                                         =  0000D0 L
    PSW.0                                                       =  0000D0 L
    PSW.1                                                       =  0000D1 L
    PSW.2                                                       =  0000D2 L
    PSW.3                                                       =  0000D3 L
    PSW.4                                                       =  0000D4 L
    PSW.5                                                       =  0000D5 L
    PSW.6                                                       =  0000D6 L
    PSW.7                                                       =  0000D7 L
    PT0                                                         =  0000B9 L
    PT1                                                         =  0000BB L
    PT2                                                         =  0000BD L
    PX0                                                         =  0000B8 L
    PX1                                                         =  0000BA L
    RB8                                                         =  00009A L
    RCAP2H                                                      =  0000CB L
    RCAP2L                                                      =  0000CA L
    RCLK                                                        =  0000CD L
    REN                                                         =  00009C L
    RI                                                          =  000098 L
    RS0                                                         =  0000D3 L
    RS1                                                         =  0000D4 L
    RXD                                                         =  0000B0 L
    SBUF                                                        =  000099 L
    SCON                                                        =  000098 L
    SCON.0                                                      =  000098 L
    SCON.1                                                      =  000099 L
    SCON.2                                                      =  00009A L
    SCON.3                                                      =  00009B L
    SCON.4                                                      =  00009C L
    SCON.5                                                      =  00009D L
    SCON.6                                                      =  00009E L
    SCON.7                                                      =  00009F L
    SM0                                                         =  00009F L
    SM1                                                         =  00009E L
    SM2                                                         =  00009D L
    SP                                                          =  000081 L
    T2CON                                                       =  0000C8 L
    T2CON.0                                                     =  0000C8 L
    T2CON.1                                                     =  0000C9 L
    T2CON.2                                                     =  0000CA L
    T2CON.3                                                     =  0000CB L
    T2CON.4                                                     =  0000CC L
    T2CON.5                                                     =  0000CD L
    T2CON.6                                                     =  0000CE L
    T2CON.7                                                     =  0000CF L
    TB8                                                         =  00009B L
    TCLK                                                        =  0000CC L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 29.
Hexadecimal [24-Bits]

Symbol Table

    TCON                                                        =  000088 L
    TCON.0                                                      =  000088 L
    TCON.1                                                      =  000089 L
    TCON.2                                                      =  00008A L
    TCON.3                                                      =  00008B L
    TCON.4                                                      =  00008C L
    TCON.5                                                      =  00008D L
    TCON.6                                                      =  00008E L
    TCON.7                                                      =  00008F L
    TF0                                                         =  00008D L
    TF1                                                         =  00008F L
    TF2                                                         =  0000CF L
    TH0                                                         =  00008C L
    TH1                                                         =  00008D L
    TH2                                                         =  0000CD L
    TI                                                          =  000099 L
    TL0                                                         =  00008A L
    TL1                                                         =  00008B L
    TL2                                                         =  0000CC L
    TMOD                                                        =  000089 L
    TR0                                                         =  00008C L
    TR1                                                         =  00008E L
    TR2                                                         =  0000CA L
    TXD                                                         =  0000B1 L
 16 XG$ATORamp$0$0                                              =  000525 GR
 16 XG$Motor_Ramp$0$0                                           =  0004A7 GR
 16 XG$Speed_response$0$0                                       =  0002EC GR
 16 XG$TargetRef_Process$0$0                                    =  0000A6 GR
 16 XG$TickCycle_1ms$0$0                                        =  000793 GR
 16 XG$zeroLoss$0$0                                             =  000802 GR
 16 _ATORamp                                                       0004A8 GR
 16 _Abs_F16                                                       000000 R
    _BusAverageVoltage                                             ****** GX
    _Fault_Detection                                               ****** GX
    _HW_One_PI                                                     ****** GX
    _HW_One_PI2                                                    ****** GX
    _KS                                                            ****** GX
    _LPFFunction                                                   ****** GX
    _LPFFunction_PARM_2                                            ****** GX
    _LPFFunction_PARM_3                                            ****** GX
 16 _Motor_Ramp                                                    0002ED GR
  A _Motor_Ramp_ref_1_304                                          00005C R
  5 _Motor_Ramp_sloc0_1_0                                          00000E R
  5 _Motor_Ramp_sloc1_1_0                                          000012 R
  5 _Motor_Ramp_sloc2_1_0                                          000014 R
  5 _Motor_Ramp_sloc3_1_0                                          000018 R
    _PI2_Init                                                      ****** GX
 16 _Speed_response                                                0000A7 GR
  A _Speed_response_F5SEG_Flag_1_286                               00005B R
  A _Speed_response_refRampOut_1_286                               000059 R
    _Sqrt_alpbet                                                   ****** GX
    _Sqrt_alpbet_PARM_2                                            ****** GX
 16 _TargetRef_Process                                             000001 GR
 16 _TickCycle_1ms                                                 000526 GR
    _User                                                          ****** GX
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 30.
Hexadecimal [24-Bits]

Symbol Table

    _VoltageComp                                                   ****** GX
    ___fs2sint                                                     ****** GX
    ___fsadd                                                       ****** GX
    ___fslt                                                        ****** GX
    ___fsmul                                                       ****** GX
    ___fssub                                                       ****** GX
    ___sint2fs                                                     ****** GX
    ___uint2fs                                                     ****** GX
    __divsint                                                      ****** GX
  8 _isCtrlPowOn                                                   000000 GR
    _mcBemf                                                        ****** GX
    _mcFaultSource                                                 ****** GX
  A _mcFocCtrl                                                     000011 GR
  A _mcPwmInput                                                    000000 GR
  5 _mcRefRamp                                                     000000 GR
    _mcState                                                       ****** GX
 16 _zeroLoss                                                      000794 GR
    a                                                           =  0000E0 L
    a.0                                                         =  0000E0 L
    a.1                                                         =  0000E1 L
    a.2                                                         =  0000E2 L
    a.3                                                         =  0000E3 L
    a.4                                                         =  0000E4 L
    a.5                                                         =  0000E5 L
    a.6                                                         =  0000E6 L
    a.7                                                         =  0000E7 L
    ac                                                          =  0000D6 L
    acc                                                         =  0000E0 L
    acc.0                                                       =  0000E0 L
    acc.1                                                       =  0000E1 L
    acc.2                                                       =  0000E2 L
    acc.3                                                       =  0000E3 L
    acc.4                                                       =  0000E4 L
    acc.5                                                       =  0000E5 L
    acc.6                                                       =  0000E6 L
    acc.7                                                       =  0000E7 L
    ar0                                                         =  000000 
    ar1                                                         =  000001 
    ar2                                                         =  000002 
    ar3                                                         =  000003 
    ar4                                                         =  000004 
    ar5                                                         =  000005 
    ar6                                                         =  000006 
    ar7                                                         =  000007 
    b                                                           =  0000F0 L
    b.0                                                         =  0000F0 L
    b.1                                                         =  0000F1 L
    b.2                                                         =  0000F2 L
    b.3                                                         =  0000F3 L
    b.4                                                         =  0000F4 L
    b.5                                                         =  0000F5 L
    b.6                                                         =  0000F6 L
    b.7                                                         =  0000F7 L
    cprl2                                                       =  0000C8 L
    ct2                                                         =  0000C9 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 31.
Hexadecimal [24-Bits]

Symbol Table

    cy                                                          =  0000D7 L
    dph                                                         =  000083 L
    dpl                                                         =  000082 L
    ea                                                          =  0000AF L
    es                                                          =  0000AC L
    et0                                                         =  0000A9 L
    et1                                                         =  0000AB L
    et2                                                         =  0000AD L
    ex0                                                         =  0000A8 L
    ex1                                                         =  0000AA L
    exen2                                                       =  0000CB L
    exf2                                                        =  0000CE L
    f0                                                          =  0000D5 L
    ie                                                          =  0000A8 L
    ie.0                                                        =  0000A8 L
    ie.1                                                        =  0000A9 L
    ie.2                                                        =  0000AA L
    ie.3                                                        =  0000AB L
    ie.4                                                        =  0000AC L
    ie.5                                                        =  0000AD L
    ie.7                                                        =  0000AF L
    ie0                                                         =  000089 L
    ie1                                                         =  00008B L
    int0                                                        =  0000B2 L
    int1                                                        =  0000B3 L
    ip                                                          =  0000B8 L
    ip.0                                                        =  0000B8 L
    ip.1                                                        =  0000B9 L
    ip.2                                                        =  0000BA L
    ip.3                                                        =  0000BB L
    ip.4                                                        =  0000BC L
    ip.5                                                        =  0000BD L
    it0                                                         =  000088 L
    it1                                                         =  00008A L
    ov                                                          =  0000D2 L
    p                                                           =  0000D0 L
    p0                                                          =  000080 L
    p0.0                                                        =  000080 L
    p0.1                                                        =  000081 L
    p0.2                                                        =  000082 L
    p0.3                                                        =  000083 L
    p0.4                                                        =  000084 L
    p0.5                                                        =  000085 L
    p0.6                                                        =  000086 L
    p0.7                                                        =  000087 L
    p1                                                          =  000090 L
    p1.0                                                        =  000090 L
    p1.1                                                        =  000091 L
    p1.2                                                        =  000092 L
    p1.3                                                        =  000093 L
    p1.4                                                        =  000094 L
    p1.5                                                        =  000095 L
    p1.6                                                        =  000096 L
    p1.7                                                        =  000097 L
    p2                                                          =  0000A0 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 32.
Hexadecimal [24-Bits]

Symbol Table

    p2.0                                                        =  0000A0 L
    p2.1                                                        =  0000A1 L
    p2.2                                                        =  0000A2 L
    p2.3                                                        =  0000A3 L
    p2.4                                                        =  0000A4 L
    p2.5                                                        =  0000A5 L
    p2.6                                                        =  0000A6 L
    p2.7                                                        =  0000A7 L
    p3                                                          =  0000B0 L
    p3.0                                                        =  0000B0 L
    p3.1                                                        =  0000B1 L
    p3.2                                                        =  0000B2 L
    p3.3                                                        =  0000B3 L
    p3.4                                                        =  0000B4 L
    p3.5                                                        =  0000B5 L
    p3.6                                                        =  0000B6 L
    p3.7                                                        =  0000B7 L
    pcon                                                        =  000087 L
    ps                                                          =  0000BC L
    psw                                                         =  0000D0 L
    psw.0                                                       =  0000D0 L
    psw.1                                                       =  0000D1 L
    psw.2                                                       =  0000D2 L
    psw.3                                                       =  0000D3 L
    psw.4                                                       =  0000D4 L
    psw.5                                                       =  0000D5 L
    psw.6                                                       =  0000D6 L
    psw.7                                                       =  0000D7 L
    pt0                                                         =  0000B9 L
    pt1                                                         =  0000BB L
    pt2                                                         =  0000BD L
    px0                                                         =  0000B8 L
    px1                                                         =  0000BA L
    rb8                                                         =  00009A L
    rcap2h                                                      =  0000CB L
    rcap2l                                                      =  0000CA L
    rclk                                                        =  0000CD L
    ren                                                         =  00009C L
    ri                                                          =  000098 L
    rs0                                                         =  0000D3 L
    rs1                                                         =  0000D4 L
    rxd                                                         =  0000B0 L
    sbuf                                                        =  000099 L
    scon                                                        =  000098 L
    scon.0                                                      =  000098 L
    scon.1                                                      =  000099 L
    scon.2                                                      =  00009A L
    scon.3                                                      =  00009B L
    scon.4                                                      =  00009C L
    scon.5                                                      =  00009D L
    scon.6                                                      =  00009E L
    scon.7                                                      =  00009F L
    sm0                                                         =  00009F L
    sm1                                                         =  00009E L
    sm2                                                         =  00009D L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 33.
Hexadecimal [24-Bits]

Symbol Table

    sp                                                          =  000081 L
    t2con                                                       =  0000C8 L
    t2con.0                                                     =  0000C8 L
    t2con.1                                                     =  0000C9 L
    t2con.2                                                     =  0000CA L
    t2con.3                                                     =  0000CB L
    t2con.4                                                     =  0000CC L
    t2con.5                                                     =  0000CD L
    t2con.6                                                     =  0000CE L
    t2con.7                                                     =  0000CF L
    tb8                                                         =  00009B L
    tclk                                                        =  0000CC L
    tcon                                                        =  000088 L
    tcon.0                                                      =  000088 L
    tcon.1                                                      =  000089 L
    tcon.2                                                      =  00008A L
    tcon.3                                                      =  00008B L
    tcon.4                                                      =  00008C L
    tcon.5                                                      =  00008D L
    tcon.6                                                      =  00008E L
    tcon.7                                                      =  00008F L
    tf0                                                         =  00008D L
    tf1                                                         =  00008F L
    tf2                                                         =  0000CF L
    th0                                                         =  00008C L
    th1                                                         =  00008D L
    th2                                                         =  0000CD L
    ti                                                          =  000099 L
    tl0                                                         =  00008A L
    tl1                                                         =  00008B L
    tl2                                                         =  0000CC L
    tmod                                                        =  000089 L
    tr0                                                         =  00008C L
    tr1                                                         =  00008E L
    tr2                                                         =  0000CA L
    txd                                                         =  0000B1 L


ASxxxx Assembler V1.0.2.1  (Intel 8051), page 34.
Hexadecimal [24-Bits]

Area Table

   0 _CODE                                      size      0   flags    0
   1 RSEG                                       size      0   flags    8
   2 RSEG0                                      size      0   flags    8
   3 RSEG1                                      size      0   flags    8
   4 REG_BANK_0                                 size      8   flags    4
   5 DSEG                                       size     1C   flags    0
   6 ISEG                                       size      0   flags    0
   7 IABS                                       size      0   flags    8
   8 BSEG                                       size      1   flags   80
   9 PSEG                                       size      0   flags   50
   A XSEG                                       size     5E   flags   40
   B XABS                                       size      0   flags   48
   C XISEG                                      size      0   flags   40
   D HOME                                       size      0   flags   20
   E GSINIT0                                    size      0   flags   20
   F GSINIT1                                    size      0   flags   20
  10 GSINIT2                                    size      0   flags   20
  11 GSINIT3                                    size      0   flags   20
  12 GSINIT4                                    size      0   flags   20
  13 GSINIT5                                    size      0   flags   20
  14 GSINIT                                     size      D   flags   20
  15 GSFINAL                                    size      0   flags   20
  16 CSEG                                       size    803   flags   20
  17 CONST                                      size      0   flags   20
  18 XINIT                                      size      0   flags   20
  19 CABS                                       size      0   flags   28

