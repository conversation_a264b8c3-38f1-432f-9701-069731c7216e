ASxxxx Assembler V1.0.2.1  (Intel 8051), page 1.
Hexadecimal [24-Bits]

Symbol Table

    .__.$$$.                                                    =  002710 L
    .__.ABS.                                                    =  000000 G
    .__.CPU.                                                    =  000000 L
    .__.H$L.                                                    =  000001 L
    A                                                           =  0000E0 L
 16 A$SMDU$137                                                     000000 GR
 16 A$SMDU$138                                                     000003 GR
 16 A$SMDU$139                                                     000005 GR
 16 A$SMDU$140                                                     000006 GR
 16 A$SMDU$141                                                     000007 GR
 16 A$SMDU$142                                                     000008 GR
 16 A$SMDU$145                                                     000009 GR
 16 A$SMDU$146                                                     00000C GR
 16 A$SMDU$147                                                     00000D GR
 16 A$SMDU$148                                                     00000E GR
 16 A$SMDU$149                                                     00000F GR
 16 A$SMDU$150                                                     000010 GR
 16 A$SMDU$151                                                     000011 GR
 16 A$SMDU$152                                                     000014 GR
 16 A$SMDU$153                                                     000015 GR
 16 A$SMDU$154                                                     000016 GR
 16 A$SMDU$155                                                     000017 GR
 16 A$SMDU$156                                                     000018 GR
 16 A$SMDU$159                                                     000019 GR
 16 A$SMDU$161                                                     00001C GR
 16 A$SMDU$162                                                     00001E GR
 16 A$SMDU$163                                                     000020 GR
 16 A$SMDU$164                                                     000021 GR
 16 A$SMDU$167                                                     000024 GR
 16 A$SMDU$168                                                     000027 GR
 16 A$SMDU$169                                                     000028 GR
 16 A$SMDU$170                                                     000029 GR
 16 A$SMDU$171                                                     00002A GR
 16 A$SMDU$172                                                     00002B GR
 16 A$SMDU$173                                                     00002C GR
 16 A$SMDU$174                                                     00002E GR
 16 A$SMDU$179                                                     00002F GR
 16 A$SMDU$192                                                     000030 GR
 16 A$SMDU$193                                                     000033 GR
 16 A$SMDU$194                                                     000035 GR
 16 A$SMDU$195                                                     000036 GR
 16 A$SMDU$196                                                     000037 GR
 16 A$SMDU$197                                                     000038 GR
 16 A$SMDU$200                                                     000039 GR
 16 A$SMDU$201                                                     00003C GR
 16 A$SMDU$202                                                     00003D GR
 16 A$SMDU$203                                                     00003E GR
 16 A$SMDU$204                                                     00003F GR
 16 A$SMDU$205                                                     000040 GR
 16 A$SMDU$206                                                     000041 GR
 16 A$SMDU$207                                                     000044 GR
 16 A$SMDU$208                                                     000045 GR
 16 A$SMDU$209                                                     000046 GR
 16 A$SMDU$210                                                     000047 GR
 16 A$SMDU$211                                                     000048 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 2.
Hexadecimal [24-Bits]

Symbol Table

 16 A$SMDU$214                                                     000049 GR
 16 A$SMDU$216                                                     00004C GR
 16 A$SMDU$217                                                     00004E GR
 16 A$SMDU$218                                                     000050 GR
 16 A$SMDU$219                                                     000051 GR
 16 A$SMDU$222                                                     000054 GR
 16 A$SMDU$223                                                     000057 GR
 16 A$SMDU$224                                                     000058 GR
 16 A$SMDU$225                                                     000059 GR
 16 A$SMDU$226                                                     00005A GR
 16 A$SMDU$227                                                     00005B GR
 16 A$SMDU$228                                                     00005C GR
 16 A$SMDU$229                                                     00005E GR
 16 A$SMDU$234                                                     00005F GR
 16 A$SMDU$249                                                     000060 GR
 16 A$SMDU$250                                                     000063 GR
 16 A$SMDU$251                                                     000065 GR
 16 A$SMDU$252                                                     000066 GR
 16 A$SMDU$253                                                     000067 GR
 16 A$SMDU$254                                                     000068 GR
 16 A$SMDU$257                                                     000069 GR
 16 A$SMDU$258                                                     00006C GR
 16 A$SMDU$259                                                     00006D GR
 16 A$SMDU$260                                                     00006E GR
 16 A$SMDU$261                                                     000070 GR
 16 A$SMDU$262                                                     000071 GR
 16 A$SMDU$263                                                     000073 GR
 16 A$SMDU$264                                                     000076 GR
 16 A$SMDU$265                                                     000077 GR
 16 A$SMDU$266                                                     000078 GR
 16 A$SMDU$267                                                     000079 GR
 16 A$SMDU$270                                                     00007A GR
 16 A$SMDU$271                                                     00007D GR
 16 A$SMDU$272                                                     00007E GR
 16 A$SMDU$273                                                     00007F GR
 16 A$SMDU$274                                                     000080 GR
 16 A$SMDU$275                                                     000081 GR
 16 A$SMDU$276                                                     000082 GR
 16 A$SMDU$277                                                     000085 GR
 16 A$SMDU$278                                                     000086 GR
 16 A$SMDU$279                                                     000087 GR
 16 A$SMDU$280                                                     000088 GR
 16 A$SMDU$281                                                     000089 GR
 16 A$SMDU$284                                                     00008A GR
 16 A$SMDU$285                                                     00008D GR
 16 A$SMDU$286                                                     00008E GR
 16 A$SMDU$287                                                     00008F GR
 16 A$SMDU$288                                                     000090 GR
 16 A$SMDU$289                                                     000091 GR
 16 A$SMDU$290                                                     000092 GR
 16 A$SMDU$291                                                     000095 GR
 16 A$SMDU$292                                                     000096 GR
 16 A$SMDU$293                                                     000097 GR
 16 A$SMDU$294                                                     000098 GR
 16 A$SMDU$295                                                     000099 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 3.
Hexadecimal [24-Bits]

Symbol Table

 16 A$SMDU$298                                                     00009A GR
 16 A$SMDU$300                                                     00009D GR
 16 A$SMDU$301                                                     00009F GR
 16 A$SMDU$302                                                     0000A1 GR
 16 A$SMDU$303                                                     0000A2 GR
 16 A$SMDU$306                                                     0000A5 GR
 16 A$SMDU$307                                                     0000A8 GR
 16 A$SMDU$308                                                     0000A9 GR
 16 A$SMDU$309                                                     0000AA GR
 16 A$SMDU$310                                                     0000AB GR
 16 A$SMDU$311                                                     0000AC GR
 16 A$SMDU$312                                                     0000AD GR
 16 A$SMDU$313                                                     0000AF GR
 16 A$SMDU$318                                                     0000B0 GR
 16 A$SMDU$332                                                     0000B1 GR
 16 A$SMDU$333                                                     0000B4 GR
 16 A$SMDU$334                                                     0000B6 GR
 16 A$SMDU$335                                                     0000B7 GR
 16 A$SMDU$336                                                     0000B8 GR
 16 A$SMDU$337                                                     0000B9 GR
 16 A$SMDU$340                                                     0000BA GR
 16 A$SMDU$341                                                     0000BD GR
 16 A$SMDU$342                                                     0000BE GR
 16 A$SMDU$343                                                     0000BF GR
 16 A$SMDU$344                                                     0000C0 GR
 16 A$SMDU$345                                                     0000C1 GR
 16 A$SMDU$346                                                     0000C2 GR
 16 A$SMDU$347                                                     0000C5 GR
 16 A$SMDU$348                                                     0000C6 GR
 16 A$SMDU$349                                                     0000C7 GR
 16 A$SMDU$350                                                     0000C8 GR
 16 A$SMDU$351                                                     0000C9 GR
 16 A$SMDU$354                                                     0000CA GR
 16 A$SMDU$355                                                     0000CD GR
 16 A$SMDU$356                                                     0000CE GR
 16 A$SMDU$357                                                     0000CF GR
 16 A$SMDU$358                                                     0000D0 GR
 16 A$SMDU$359                                                     0000D1 GR
 16 A$SMDU$360                                                     0000D2 GR
 16 A$SMDU$361                                                     0000D5 GR
 16 A$SMDU$362                                                     0000D6 GR
 16 A$SMDU$363                                                     0000D7 GR
 16 A$SMDU$364                                                     0000D8 GR
 16 A$SMDU$365                                                     0000D9 GR
 16 A$SMDU$368                                                     0000DA GR
 16 A$SMDU$370                                                     0000DD GR
 16 A$SMDU$371                                                     0000DF GR
 16 A$SMDU$372                                                     0000E1 GR
 16 A$SMDU$373                                                     0000E2 GR
 16 A$SMDU$376                                                     0000E5 GR
 16 A$SMDU$377                                                     0000E8 GR
 16 A$SMDU$378                                                     0000E9 GR
 16 A$SMDU$379                                                     0000EA GR
 16 A$SMDU$380                                                     0000EB GR
 16 A$SMDU$381                                                     0000EC GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 4.
Hexadecimal [24-Bits]

Symbol Table

 16 A$SMDU$382                                                     0000ED GR
 16 A$SMDU$383                                                     0000EF GR
 16 A$SMDU$388                                                     0000F0 GR
    A.0                                                         =  0000E0 L
    A.1                                                         =  0000E1 L
    A.2                                                         =  0000E2 L
    A.3                                                         =  0000E3 L
    A.4                                                         =  0000E4 L
    A.5                                                         =  0000E5 L
    A.6                                                         =  0000E6 L
    A.7                                                         =  0000E7 L
    AC                                                          =  0000D6 L
    ACC                                                         =  0000E0 L
    ACC.0                                                       =  0000E0 L
    ACC.1                                                       =  0000E1 L
    ACC.2                                                       =  0000E2 L
    ACC.3                                                       =  0000E3 L
    ACC.4                                                       =  0000E4 L
    ACC.5                                                       =  0000E5 L
    ACC.6                                                       =  0000E6 L
    ACC.7                                                       =  0000E7 L
    B                                                           =  0000F0 L
    B.0                                                         =  0000F0 L
    B.1                                                         =  0000F1 L
    B.2                                                         =  0000F2 L
    B.3                                                         =  0000F3 L
    B.4                                                         =  0000F4 L
    B.5                                                         =  0000F5 L
    B.6                                                         =  0000F6 L
    B.7                                                         =  0000F7 L
 16 C$SMDU.c$130$1$265                                          =  0000B1 GR
 16 C$SMDU.c$132$1$265                                          =  0000BA GR
 16 C$SMDU.c$133$1$265                                          =  0000CA GR
 16 C$SMDU.c$134$3$267                                          =  0000DA GR
 16 C$SMDU.c$135$1$265                                          =  0000E5 GR
 16 C$SMDU.c$136$1$265                                          =  0000F0 GR
 16 C$SMDU.c$25$0$241                                           =  000000 GR
 16 C$SMDU.c$27$1$241                                           =  000009 GR
 16 C$SMDU.c$28$3$243                                           =  000019 GR
 16 C$SMDU.c$29$1$241                                           =  000024 GR
 16 C$SMDU.c$30$1$241                                           =  00002F GR
 16 C$SMDU.c$37$1$245                                           =  000030 GR
 16 C$SMDU.c$39$1$245                                           =  000039 GR
 16 C$SMDU.c$40$3$247                                           =  000049 GR
 16 C$SMDU.c$41$1$245                                           =  000054 GR
 16 C$SMDU.c$42$1$245                                           =  00005F GR
 16 C$SMDU.c$68$1$253                                           =  000060 GR
 16 C$SMDU.c$70$1$253                                           =  000069 GR
 16 C$SMDU.c$71$1$253                                           =  00007A GR
 16 C$SMDU.c$72$1$253                                           =  00008A GR
 16 C$SMDU.c$73$3$255                                           =  00009A GR
 16 C$SMDU.c$74$1$253                                           =  0000A5 GR
 16 C$SMDU.c$75$1$253                                           =  0000B0 GR
    CPRL2                                                       =  0000C8 L
    CT2                                                         =  0000C9 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 5.
Hexadecimal [24-Bits]

Symbol Table

    CY                                                          =  0000D7 L
    DPH                                                         =  000083 L
    DPL                                                         =  000082 L
    EA                                                          =  0000AF L
    ES                                                          =  0000AC L
    ET0                                                         =  0000A9 L
    ET1                                                         =  0000AB L
    ET2                                                         =  0000AD L
    EX0                                                         =  0000A8 L
    EX1                                                         =  0000AA L
    EXEN2                                                       =  0000CB L
    EXF2                                                        =  0000CE L
    F0                                                          =  0000D5 L
 16 G$HW_One_PI$0$0                                             =  000000 GR
 16 G$HW_One_PI2$0$0                                            =  000030 GR
 16 G$LPFFunction$0$0                                           =  000060 GR
    G$MDU_CR$0$0                                                =  0000C1 G
 16 G$Sqrt_alpbet$0$0                                           =  0000B1 GR
    IE                                                          =  0000A8 L
    IE.0                                                        =  0000A8 L
    IE.1                                                        =  0000A9 L
    IE.2                                                        =  0000AA L
    IE.3                                                        =  0000AB L
    IE.4                                                        =  0000AC L
    IE.5                                                        =  0000AD L
    IE.7                                                        =  0000AF L
    IE0                                                         =  000089 L
    IE1                                                         =  00008B L
    INT0                                                        =  0000B2 L
    INT1                                                        =  0000B3 L
    IP                                                          =  0000B8 L
    IP.0                                                        =  0000B8 L
    IP.1                                                        =  0000B9 L
    IP.2                                                        =  0000BA L
    IP.3                                                        =  0000BB L
    IP.4                                                        =  0000BC L
    IP.5                                                        =  0000BD L
    IT0                                                         =  000088 L
    IT1                                                         =  00008A L
  A LSMDU.HW_One_PI$Xn1$1$240                                   =  000000 GR
  A LSMDU.HW_One_PI2$Xn1$1$244                                  =  000002 GR
  A LSMDU.LPFFunction$K$1$252                                   =  000006 GR
  A LSMDU.LPFFunction$Xn0$1$252                                 =  000004 GR
  A LSMDU.LPFFunction$Xn1$1$252                                 =  000007 GR
  A LSMDU.Sqrt_alpbet$i_alp$1$264                               =  00000B GR
  A LSMDU.Sqrt_alpbet$i_bet$1$264                               =  000009 GR
    OV                                                          =  0000D2 L
    P                                                           =  0000D0 L
    P0                                                          =  000080 L
    P0.0                                                        =  000080 L
    P0.1                                                        =  000081 L
    P0.2                                                        =  000082 L
    P0.3                                                        =  000083 L
    P0.4                                                        =  000084 L
    P0.5                                                        =  000085 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 6.
Hexadecimal [24-Bits]

Symbol Table

    P0.6                                                        =  000086 L
    P0.7                                                        =  000087 L
    P1                                                          =  000090 L
    P1.0                                                        =  000090 L
    P1.1                                                        =  000091 L
    P1.2                                                        =  000092 L
    P1.3                                                        =  000093 L
    P1.4                                                        =  000094 L
    P1.5                                                        =  000095 L
    P1.6                                                        =  000096 L
    P1.7                                                        =  000097 L
    P2                                                          =  0000A0 L
    P2.0                                                        =  0000A0 L
    P2.1                                                        =  0000A1 L
    P2.2                                                        =  0000A2 L
    P2.3                                                        =  0000A3 L
    P2.4                                                        =  0000A4 L
    P2.5                                                        =  0000A5 L
    P2.6                                                        =  0000A6 L
    P2.7                                                        =  0000A7 L
    P3                                                          =  0000B0 L
    P3.0                                                        =  0000B0 L
    P3.1                                                        =  0000B1 L
    P3.2                                                        =  0000B2 L
    P3.3                                                        =  0000B3 L
    P3.4                                                        =  0000B4 L
    P3.5                                                        =  0000B5 L
    P3.6                                                        =  0000B6 L
    P3.7                                                        =  0000B7 L
    PCON                                                        =  000087 L
    PS                                                          =  0000BC L
    PSW                                                         =  0000D0 L
    PSW.0                                                       =  0000D0 L
    PSW.1                                                       =  0000D1 L
    PSW.2                                                       =  0000D2 L
    PSW.3                                                       =  0000D3 L
    PSW.4                                                       =  0000D4 L
    PSW.5                                                       =  0000D5 L
    PSW.6                                                       =  0000D6 L
    PSW.7                                                       =  0000D7 L
    PT0                                                         =  0000B9 L
    PT1                                                         =  0000BB L
    PT2                                                         =  0000BD L
    PX0                                                         =  0000B8 L
    PX1                                                         =  0000BA L
    RB8                                                         =  00009A L
    RCAP2H                                                      =  0000CB L
    RCAP2L                                                      =  0000CA L
    RCLK                                                        =  0000CD L
    REN                                                         =  00009C L
    RI                                                          =  000098 L
    RS0                                                         =  0000D3 L
    RS1                                                         =  0000D4 L
    RXD                                                         =  0000B0 L
    SBUF                                                        =  000099 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 7.
Hexadecimal [24-Bits]

Symbol Table

    SCON                                                        =  000098 L
    SCON.0                                                      =  000098 L
    SCON.1                                                      =  000099 L
    SCON.2                                                      =  00009A L
    SCON.3                                                      =  00009B L
    SCON.4                                                      =  00009C L
    SCON.5                                                      =  00009D L
    SCON.6                                                      =  00009E L
    SCON.7                                                      =  00009F L
    SM0                                                         =  00009F L
    SM1                                                         =  00009E L
    SM2                                                         =  00009D L
    SP                                                          =  000081 L
    T2CON                                                       =  0000C8 L
    T2CON.0                                                     =  0000C8 L
    T2CON.1                                                     =  0000C9 L
    T2CON.2                                                     =  0000CA L
    T2CON.3                                                     =  0000CB L
    T2CON.4                                                     =  0000CC L
    T2CON.5                                                     =  0000CD L
    T2CON.6                                                     =  0000CE L
    T2CON.7                                                     =  0000CF L
    TB8                                                         =  00009B L
    TCLK                                                        =  0000CC L
    TCON                                                        =  000088 L
    TCON.0                                                      =  000088 L
    TCON.1                                                      =  000089 L
    TCON.2                                                      =  00008A L
    TCON.3                                                      =  00008B L
    TCON.4                                                      =  00008C L
    TCON.5                                                      =  00008D L
    TCON.6                                                      =  00008E L
    TCON.7                                                      =  00008F L
    TF0                                                         =  00008D L
    TF1                                                         =  00008F L
    TF2                                                         =  0000CF L
    TH0                                                         =  00008C L
    TH1                                                         =  00008D L
    TH2                                                         =  0000CD L
    TI                                                          =  000099 L
    TL0                                                         =  00008A L
    TL1                                                         =  00008B L
    TL2                                                         =  0000CC L
    TMOD                                                        =  000089 L
    TR0                                                         =  00008C L
    TR1                                                         =  00008E L
    TR2                                                         =  0000CA L
    TXD                                                         =  0000B1 L
 16 XG$HW_One_PI$0$0                                            =  00002F GR
 16 XG$HW_One_PI2$0$0                                           =  00005F GR
 16 XG$LPFFunction$0$0                                          =  0000B0 GR
 16 XG$Sqrt_alpbet$0$0                                          =  0000F0 GR
 16 _HW_One_PI                                                     000000 GR
 16 _HW_One_PI2                                                    000030 GR
  A _HW_One_PI2_Xn1_1_244                                          000002 R
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 8.
Hexadecimal [24-Bits]

Symbol Table

  A _HW_One_PI_Xn1_1_240                                           000000 R
 16 _LPFFunction                                                   000060 GR
  A _LPFFunction_PARM_2                                            000004 GR
  A _LPFFunction_PARM_3                                            000006 GR
  A _LPFFunction_Xn1_1_252                                         000007 R
    _MDU_CR                                                     =  0000C1 G
 16 _Sqrt_alpbet                                                   0000B1 GR
  A _Sqrt_alpbet_PARM_2                                            000009 GR
  A _Sqrt_alpbet_i_alp_1_264                                       00000B R
    a                                                           =  0000E0 L
    a.0                                                         =  0000E0 L
    a.1                                                         =  0000E1 L
    a.2                                                         =  0000E2 L
    a.3                                                         =  0000E3 L
    a.4                                                         =  0000E4 L
    a.5                                                         =  0000E5 L
    a.6                                                         =  0000E6 L
    a.7                                                         =  0000E7 L
    ac                                                          =  0000D6 L
    acc                                                         =  0000E0 L
    acc.0                                                       =  0000E0 L
    acc.1                                                       =  0000E1 L
    acc.2                                                       =  0000E2 L
    acc.3                                                       =  0000E3 L
    acc.4                                                       =  0000E4 L
    acc.5                                                       =  0000E5 L
    acc.6                                                       =  0000E6 L
    acc.7                                                       =  0000E7 L
    ar0                                                         =  000000 
    ar1                                                         =  000001 
    ar2                                                         =  000002 
    ar3                                                         =  000003 
    ar4                                                         =  000004 
    ar5                                                         =  000005 
    ar6                                                         =  000006 
    ar7                                                         =  000007 
    b                                                           =  0000F0 L
    b.0                                                         =  0000F0 L
    b.1                                                         =  0000F1 L
    b.2                                                         =  0000F2 L
    b.3                                                         =  0000F3 L
    b.4                                                         =  0000F4 L
    b.5                                                         =  0000F5 L
    b.6                                                         =  0000F6 L
    b.7                                                         =  0000F7 L
    cprl2                                                       =  0000C8 L
    ct2                                                         =  0000C9 L
    cy                                                          =  0000D7 L
    dph                                                         =  000083 L
    dpl                                                         =  000082 L
    ea                                                          =  0000AF L
    es                                                          =  0000AC L
    et0                                                         =  0000A9 L
    et1                                                         =  0000AB L
    et2                                                         =  0000AD L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 9.
Hexadecimal [24-Bits]

Symbol Table

    ex0                                                         =  0000A8 L
    ex1                                                         =  0000AA L
    exen2                                                       =  0000CB L
    exf2                                                        =  0000CE L
    f0                                                          =  0000D5 L
    ie                                                          =  0000A8 L
    ie.0                                                        =  0000A8 L
    ie.1                                                        =  0000A9 L
    ie.2                                                        =  0000AA L
    ie.3                                                        =  0000AB L
    ie.4                                                        =  0000AC L
    ie.5                                                        =  0000AD L
    ie.7                                                        =  0000AF L
    ie0                                                         =  000089 L
    ie1                                                         =  00008B L
    int0                                                        =  0000B2 L
    int1                                                        =  0000B3 L
    ip                                                          =  0000B8 L
    ip.0                                                        =  0000B8 L
    ip.1                                                        =  0000B9 L
    ip.2                                                        =  0000BA L
    ip.3                                                        =  0000BB L
    ip.4                                                        =  0000BC L
    ip.5                                                        =  0000BD L
    it0                                                         =  000088 L
    it1                                                         =  00008A L
    ov                                                          =  0000D2 L
    p                                                           =  0000D0 L
    p0                                                          =  000080 L
    p0.0                                                        =  000080 L
    p0.1                                                        =  000081 L
    p0.2                                                        =  000082 L
    p0.3                                                        =  000083 L
    p0.4                                                        =  000084 L
    p0.5                                                        =  000085 L
    p0.6                                                        =  000086 L
    p0.7                                                        =  000087 L
    p1                                                          =  000090 L
    p1.0                                                        =  000090 L
    p1.1                                                        =  000091 L
    p1.2                                                        =  000092 L
    p1.3                                                        =  000093 L
    p1.4                                                        =  000094 L
    p1.5                                                        =  000095 L
    p1.6                                                        =  000096 L
    p1.7                                                        =  000097 L
    p2                                                          =  0000A0 L
    p2.0                                                        =  0000A0 L
    p2.1                                                        =  0000A1 L
    p2.2                                                        =  0000A2 L
    p2.3                                                        =  0000A3 L
    p2.4                                                        =  0000A4 L
    p2.5                                                        =  0000A5 L
    p2.6                                                        =  0000A6 L
    p2.7                                                        =  0000A7 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 10.
Hexadecimal [24-Bits]

Symbol Table

    p3                                                          =  0000B0 L
    p3.0                                                        =  0000B0 L
    p3.1                                                        =  0000B1 L
    p3.2                                                        =  0000B2 L
    p3.3                                                        =  0000B3 L
    p3.4                                                        =  0000B4 L
    p3.5                                                        =  0000B5 L
    p3.6                                                        =  0000B6 L
    p3.7                                                        =  0000B7 L
    pcon                                                        =  000087 L
    ps                                                          =  0000BC L
    psw                                                         =  0000D0 L
    psw.0                                                       =  0000D0 L
    psw.1                                                       =  0000D1 L
    psw.2                                                       =  0000D2 L
    psw.3                                                       =  0000D3 L
    psw.4                                                       =  0000D4 L
    psw.5                                                       =  0000D5 L
    psw.6                                                       =  0000D6 L
    psw.7                                                       =  0000D7 L
    pt0                                                         =  0000B9 L
    pt1                                                         =  0000BB L
    pt2                                                         =  0000BD L
    px0                                                         =  0000B8 L
    px1                                                         =  0000BA L
    rb8                                                         =  00009A L
    rcap2h                                                      =  0000CB L
    rcap2l                                                      =  0000CA L
    rclk                                                        =  0000CD L
    ren                                                         =  00009C L
    ri                                                          =  000098 L
    rs0                                                         =  0000D3 L
    rs1                                                         =  0000D4 L
    rxd                                                         =  0000B0 L
    sbuf                                                        =  000099 L
    scon                                                        =  000098 L
    scon.0                                                      =  000098 L
    scon.1                                                      =  000099 L
    scon.2                                                      =  00009A L
    scon.3                                                      =  00009B L
    scon.4                                                      =  00009C L
    scon.5                                                      =  00009D L
    scon.6                                                      =  00009E L
    scon.7                                                      =  00009F L
    sm0                                                         =  00009F L
    sm1                                                         =  00009E L
    sm2                                                         =  00009D L
    sp                                                          =  000081 L
    t2con                                                       =  0000C8 L
    t2con.0                                                     =  0000C8 L
    t2con.1                                                     =  0000C9 L
    t2con.2                                                     =  0000CA L
    t2con.3                                                     =  0000CB L
    t2con.4                                                     =  0000CC L
    t2con.5                                                     =  0000CD L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 11.
Hexadecimal [24-Bits]

Symbol Table

    t2con.6                                                     =  0000CE L
    t2con.7                                                     =  0000CF L
    tb8                                                         =  00009B L
    tclk                                                        =  0000CC L
    tcon                                                        =  000088 L
    tcon.0                                                      =  000088 L
    tcon.1                                                      =  000089 L
    tcon.2                                                      =  00008A L
    tcon.3                                                      =  00008B L
    tcon.4                                                      =  00008C L
    tcon.5                                                      =  00008D L
    tcon.6                                                      =  00008E L
    tcon.7                                                      =  00008F L
    tf0                                                         =  00008D L
    tf1                                                         =  00008F L
    tf2                                                         =  0000CF L
    th0                                                         =  00008C L
    th1                                                         =  00008D L
    th2                                                         =  0000CD L
    ti                                                          =  000099 L
    tl0                                                         =  00008A L
    tl1                                                         =  00008B L
    tl2                                                         =  0000CC L
    tmod                                                        =  000089 L
    tr0                                                         =  00008C L
    tr1                                                         =  00008E L
    tr2                                                         =  0000CA L
    txd                                                         =  0000B1 L


ASxxxx Assembler V1.0.2.1  (Intel 8051), page 12.
Hexadecimal [24-Bits]

Area Table

   0 _CODE                                      size      0   flags    0
   1 RSEG                                       size      0   flags    8
   2 RSEG0                                      size      0   flags    8
   3 RSEG1                                      size      0   flags    8
   4 REG_BANK_0                                 size      8   flags    4
   5 DSEG                                       size      0   flags    0
   6 ISEG                                       size      0   flags    0
   7 IABS                                       size      0   flags    8
   8 BSEG                                       size      0   flags   80
   9 PSEG                                       size      0   flags   50
   A XSEG                                       size      D   flags   40
   B XABS                                       size      0   flags   48
   C XISEG                                      size      0   flags   40
   D HOME                                       size      0   flags   20
   E GSINIT0                                    size      0   flags   20
   F GSINIT1                                    size      0   flags   20
  10 GSINIT2                                    size      0   flags   20
  11 GSINIT3                                    size      0   flags   20
  12 GSINIT4                                    size      0   flags   20
  13 GSINIT5                                    size      0   flags   20
  14 GSINIT                                     size      0   flags   20
  15 GSFINAL                                    size      0   flags   20
  16 CSEG                                       size     F1   flags   20
  17 CONST                                      size      0   flags   20
  18 XINIT                                      size      0   flags   20
  19 CABS                                       size      0   flags   28

