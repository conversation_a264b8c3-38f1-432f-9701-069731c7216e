/**
 * @copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
 * @file      MyProject.h
 * <AUTHOR> Appliction Team
 * @note      Last modify author is <PERSON>
 * @since     2017-12-27
 * @date      2022-07-14
 * @brief     This file contains all the common data types used for Motor Control.  
 *                      
 */


/* Define to prevent recursive inclusion --------------------------------------------------------*/
#ifndef __MYPROJECT_H_
#define __MYPROJECT_H_

/* Includes -------------------------------------------------------------------------------------*/
#include "FU65xx_2.h"

#include <Definition.h>
#include <Customer.h>
#include <Parameter.h>
#include <Protect.h>
#include <Customer_Debug.h>

#include <AMP.h>
#include <GPIO.h>
#include <SPI.h>
#include <ADC.h>
#include <TSD.h>
#include <CMP.h>
#include <AMP.h>
#include <Driver.h>
#include <UART.h>
#include <CRC.h>
#include <GPIO.h>
#include <PIInit.h>
#include <TIMER.h>
#include <DMA.h>
#include "FU68xx_5_Flash.h"
#include <AddFunction.h>
#include <FOCTailDect.h>
#include <RSDDetect.h>
#include <MotorControlFunction.h>
#include <MotorControl.h>
#include <PosCheck.h>
#include <SMDU.h>
#include <BEMFDetect.h>
#include <VoltageCompensation.h>
#include <LED.h>
#include <KeyScan.h>
#include <EXTIInit.h>
#include <mcFieldWeaken.h>

#endif