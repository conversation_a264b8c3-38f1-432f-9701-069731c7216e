ASxxxx Assembler V1.0.2.1  (Intel 8051), page 1.
Hexadecimal [24-Bits]

Symbol Table

    .__.$$$.                                                    =  002710 L
    .__.ABS.                                                    =  000000 G
    .__.CPU.                                                    =  000000 L
    .__.H$L.                                                    =  000001 L
    A                                                           =  0000E0 L
  E A$main$100                                                     00001B GR
  E A$main$102                                                     000023 GR
  E A$main$104                                                     00002B GR
  E A$main$106                                                     000033 GR
  E A$main$108                                                     00003B GR
  E A$main$110                                                     000043 GR
  E A$main$112                                                     00004B GR
  E A$main$114                                                     000053 GR
  E A$main$116                                                     00005B GR
  E A$main$118                                                     000063 GR
 15 A$main$134                                                     000000 GR
 16 A$main$136                                                     000000 GR
  E A$main$143                                                     000066 GR
 17 A$main$167                                                     000000 GR
 17 A$main$180                                                     000001 GR
 17 A$main$183                                                     000004 GR
 17 A$main$186                                                     000007 GR
 17 A$main$189                                                     00000A GR
 17 A$main$192                                                     00000D GR
 17 A$main$195                                                     000010 GR
 17 A$main$198                                                     000013 GR
 17 A$main$201                                                     000016 GR
 17 A$main$204                                                     000019 GR
 17 A$main$207                                                     00001C GR
 17 A$main$210                                                     00001F GR
 17 A$main$213                                                     000022 GR
 17 A$main$214                                                     000025 GR
 17 A$main$215                                                     000026 GR
 17 A$main$216                                                     000028 GR
 17 A$main$217                                                     000029 GR
 17 A$main$220                                                     00002A GR
 17 A$main$225                                                     00002C GR
 17 A$main$238                                                     00002D GR
 17 A$main$241                                                     000030 GR
 17 A$main$244                                                     000033 GR
 17 A$main$249                                                     000036 GR
 17 A$main$264                                                     000037 GR
 17 A$main$265                                                     00003A GR
 17 A$main$266                                                     00003B GR
 17 A$main$267                                                     00003D GR
 17 A$main$268                                                     00003E GR
 17 A$main$269                                                     00003F GR
 17 A$main$272                                                     000042 GR
 17 A$main$273                                                     000045 GR
 17 A$main$274                                                     000046 GR
 17 A$main$275                                                     000047 GR
 17 A$main$276                                                     000048 GR
 17 A$main$279                                                     00004B GR
 17 A$main$280                                                     00004E GR
 17 A$main$281                                                     00004F GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 2.
Hexadecimal [24-Bits]

Symbol Table

 17 A$main$282                                                     000050 GR
 17 A$main$283                                                     000051 GR
 17 A$main$286                                                     000054 GR
 17 A$main$287                                                     000057 GR
 17 A$main$288                                                     000058 GR
 17 A$main$289                                                     000059 GR
 17 A$main$290                                                     00005A GR
 17 A$main$293                                                     00005D GR
 17 A$main$294                                                     000060 GR
 17 A$main$295                                                     000061 GR
 17 A$main$296                                                     000062 GR
 17 A$main$297                                                     000063 GR
 17 A$main$298                                                     000066 GR
 17 A$main$299                                                     000069 GR
 17 A$main$300                                                     00006A GR
 17 A$main$305                                                     00006B GR
 17 A$main$318                                                     00006C GR
 17 A$main$319                                                     00006E GR
 17 A$main$321                                                     000070 GR
 17 A$main$322                                                     000071 GR
 17 A$main$323                                                     000074 GR
 17 A$main$325                                                     000075 GR
 17 A$main$326                                                     000076 GR
 17 A$main$327                                                     000077 GR
 17 A$main$330                                                     000079 GR
 17 A$main$333                                                     00007C GR
 17 A$main$339                                                     00007F GR
 17 A$main$340                                                     000082 GR
 17 A$main$341                                                     000083 GR
 17 A$main$344                                                     000085 GR
 17 A$main$345                                                     000088 GR
 17 A$main$349                                                     00008A GR
 17 A$main$352                                                     00008D GR
 17 A$main$353                                                     00008F GR
 17 A$main$356                                                     000091 GR
 17 A$main$359                                                     000094 GR
 17 A$main$364                                                     000097 GR
  E A$main$93                                                      000000 GR
  E A$main$94                                                      000003 GR
  E A$main$96                                                      00000B GR
  E A$main$98                                                      000013 GR
    A.0                                                         =  0000E0 L
    A.1                                                         =  0000E1 L
    A.2                                                         =  0000E2 L
    A.3                                                         =  0000E3 L
    A.4                                                         =  0000E4 L
    A.5                                                         =  0000E5 L
    A.6                                                         =  0000E6 L
    A.7                                                         =  0000E7 L
    AC                                                          =  0000D6 L
    ACC                                                         =  0000E0 L
    ACC.0                                                       =  0000E0 L
    ACC.1                                                       =  0000E1 L
    ACC.2                                                       =  0000E2 L
    ACC.3                                                       =  0000E3 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 3.
Hexadecimal [24-Bits]

Symbol Table

    ACC.4                                                       =  0000E4 L
    ACC.5                                                       =  0000E5 L
    ACC.6                                                       =  0000E6 L
    ACC.7                                                       =  0000E7 L
    B                                                           =  0000F0 L
    B.0                                                         =  0000F0 L
    B.1                                                         =  0000F1 L
    B.2                                                         =  0000F2 L
    B.3                                                         =  0000F3 L
    B.4                                                         =  0000F4 L
    B.5                                                         =  0000F5 L
    B.6                                                         =  0000F6 L
    B.7                                                         =  0000F7 L
 17 C$main.c$103$1$253                                          =  000037 GR
 17 C$main.c$123$2$254                                          =  000037 GR
 17 C$main.c$124$2$254                                          =  000037 GR
 17 C$main.c$142$2$255                                          =  000042 GR
 17 C$main.c$143$2$255                                          =  00004B GR
 17 C$main.c$158$1$253                                          =  000054 GR
 17 C$main.c$161$1$253                                          =  00005D GR
 17 C$main.c$162$1$253                                          =  00006B GR
 17 C$main.c$168$1$257                                          =  00006C GR
 15 C$main.c$17$1$257                                           =  000000 GR
 17 C$main.c$173$1$257                                          =  00006C GR
 17 C$main.c$176$1$257                                          =  000079 GR
 17 C$main.c$178$1$257                                          =  00007C GR
 17 C$main.c$184$1$257                                          =  00007F GR
 17 C$main.c$187$2$259                                          =  00007F GR
 17 C$main.c$189$3$260                                          =  000085 GR
 17 C$main.c$194$3$261                                          =  00008A GR
 17 C$main.c$196$3$261                                          =  00008D GR
 17 C$main.c$198$4$262                                          =  000091 GR
 17 C$main.c$199$4$262                                          =  000094 GR
 17 C$main.c$203$1$257                                          =  000097 GR
 17 C$main.c$31$0$246                                           =  000000 GR
 17 C$main.c$50$0$248                                           =  000001 GR
 17 C$main.c$57$1$248                                           =  000001 GR
 17 C$main.c$59$1$248                                           =  000004 GR
 17 C$main.c$60$1$248                                           =  000007 GR
 17 C$main.c$61$1$248                                           =  00000A GR
 17 C$main.c$62$1$248                                           =  00000D GR
 17 C$main.c$63$1$248                                           =  000010 GR
 17 C$main.c$67$2$249                                           =  000013 GR
 17 C$main.c$72$1$248                                           =  000016 GR
 17 C$main.c$74$1$248                                           =  000019 GR
 17 C$main.c$76$1$248                                           =  00001C GR
 17 C$main.c$77$1$248                                           =  00001F GR
 17 C$main.c$78$1$248                                           =  000022 GR
 17 C$main.c$79$1$248                                           =  00002A GR
 17 C$main.c$80$1$248                                           =  00002C GR
 17 C$main.c$86$1$251                                           =  00002D GR
 17 C$main.c$88$1$251                                           =  00002D GR
 17 C$main.c$95$1$251                                           =  000030 GR
 17 C$main.c$96$1$251                                           =  000033 GR
 17 C$main.c$97$1$251                                           =  000036 GR
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 4.
Hexadecimal [24-Bits]

Symbol Table

    CPRL2                                                       =  0000C8 L
    CT2                                                         =  0000C9 L
    CY                                                          =  0000D7 L
    DPH                                                         =  000083 L
    DPL                                                         =  000082 L
    EA                                                          =  0000AF L
    ES                                                          =  0000AC L
    ET0                                                         =  0000A9 L
    ET1                                                         =  0000AB L
    ET2                                                         =  0000AD L
    EX0                                                         =  0000A8 L
    EX1                                                         =  0000AA L
    EXEN2                                                       =  0000CB L
    EXF2                                                        =  0000CE L
    F0                                                          =  0000D5 L
 17 Fmain$DebugSet$0$0                                          =  000000 GR
 17 Fmain$HardwareInit$0$0                                      =  000001 GR
 17 Fmain$SoftwareInit$0$0                                      =  00002D GR
    G$EA$0$0                                                    =  0000AF G
    G$IP2$0$0                                                   =  00008C G
 17 G$VREFConfigInit$0$0                                        =  000037 GR
  5 G$g_1mTick$0$0                                              =  000000 GR
 17 G$main$0$0                                                  =  00006C GR
    IE                                                          =  0000A8 L
    IE.0                                                        =  0000A8 L
    IE.1                                                        =  0000A9 L
    IE.2                                                        =  0000AA L
    IE.3                                                        =  0000AB L
    IE.4                                                        =  0000AC L
    IE.5                                                        =  0000AD L
    IE.7                                                        =  0000AF L
    IE0                                                         =  000089 L
    IE1                                                         =  00008B L
    INT0                                                        =  0000B2 L
    INT1                                                        =  0000B3 L
    IP                                                          =  0000B8 L
    IP.0                                                        =  0000B8 L
    IP.1                                                        =  0000B9 L
    IP.2                                                        =  0000BA L
    IP.3                                                        =  0000BB L
    IP.4                                                        =  0000BC L
    IP.5                                                        =  0000BD L
    IT0                                                         =  000088 L
    IT1                                                         =  00008A L
    OV                                                          =  0000D2 L
    P                                                           =  0000D0 L
    P0                                                          =  000080 L
    P0.0                                                        =  000080 L
    P0.1                                                        =  000081 L
    P0.2                                                        =  000082 L
    P0.3                                                        =  000083 L
    P0.4                                                        =  000084 L
    P0.5                                                        =  000085 L
    P0.6                                                        =  000086 L
    P0.7                                                        =  000087 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 5.
Hexadecimal [24-Bits]

Symbol Table

    P1                                                          =  000090 L
    P1.0                                                        =  000090 L
    P1.1                                                        =  000091 L
    P1.2                                                        =  000092 L
    P1.3                                                        =  000093 L
    P1.4                                                        =  000094 L
    P1.5                                                        =  000095 L
    P1.6                                                        =  000096 L
    P1.7                                                        =  000097 L
    P2                                                          =  0000A0 L
    P2.0                                                        =  0000A0 L
    P2.1                                                        =  0000A1 L
    P2.2                                                        =  0000A2 L
    P2.3                                                        =  0000A3 L
    P2.4                                                        =  0000A4 L
    P2.5                                                        =  0000A5 L
    P2.6                                                        =  0000A6 L
    P2.7                                                        =  0000A7 L
    P3                                                          =  0000B0 L
    P3.0                                                        =  0000B0 L
    P3.1                                                        =  0000B1 L
    P3.2                                                        =  0000B2 L
    P3.3                                                        =  0000B3 L
    P3.4                                                        =  0000B4 L
    P3.5                                                        =  0000B5 L
    P3.6                                                        =  0000B6 L
    P3.7                                                        =  0000B7 L
    PCON                                                        =  000087 L
    PS                                                          =  0000BC L
    PSW                                                         =  0000D0 L
    PSW.0                                                       =  0000D0 L
    PSW.1                                                       =  0000D1 L
    PSW.2                                                       =  0000D2 L
    PSW.3                                                       =  0000D3 L
    PSW.4                                                       =  0000D4 L
    PSW.5                                                       =  0000D5 L
    PSW.6                                                       =  0000D6 L
    PSW.7                                                       =  0000D7 L
    PT0                                                         =  0000B9 L
    PT1                                                         =  0000BB L
    PT2                                                         =  0000BD L
    PX0                                                         =  0000B8 L
    PX1                                                         =  0000BA L
    RB8                                                         =  00009A L
    RCAP2H                                                      =  0000CB L
    RCAP2L                                                      =  0000CA L
    RCLK                                                        =  0000CD L
    REN                                                         =  00009C L
    RI                                                          =  000098 L
    RS0                                                         =  0000D3 L
    RS1                                                         =  0000D4 L
    RXD                                                         =  0000B0 L
    SBUF                                                        =  000099 L
    SCON                                                        =  000098 L
    SCON.0                                                      =  000098 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 6.
Hexadecimal [24-Bits]

Symbol Table

    SCON.1                                                      =  000099 L
    SCON.2                                                      =  00009A L
    SCON.3                                                      =  00009B L
    SCON.4                                                      =  00009C L
    SCON.5                                                      =  00009D L
    SCON.6                                                      =  00009E L
    SCON.7                                                      =  00009F L
    SM0                                                         =  00009F L
    SM1                                                         =  00009E L
    SM2                                                         =  00009D L
    SP                                                          =  000081 L
    T2CON                                                       =  0000C8 L
    T2CON.0                                                     =  0000C8 L
    T2CON.1                                                     =  0000C9 L
    T2CON.2                                                     =  0000CA L
    T2CON.3                                                     =  0000CB L
    T2CON.4                                                     =  0000CC L
    T2CON.5                                                     =  0000CD L
    T2CON.6                                                     =  0000CE L
    T2CON.7                                                     =  0000CF L
    TB8                                                         =  00009B L
    TCLK                                                        =  0000CC L
    TCON                                                        =  000088 L
    TCON.0                                                      =  000088 L
    TCON.1                                                      =  000089 L
    TCON.2                                                      =  00008A L
    TCON.3                                                      =  00008B L
    TCON.4                                                      =  00008C L
    TCON.5                                                      =  00008D L
    TCON.6                                                      =  00008E L
    TCON.7                                                      =  00008F L
    TF0                                                         =  00008D L
    TF1                                                         =  00008F L
    TF2                                                         =  0000CF L
    TH0                                                         =  00008C L
    TH1                                                         =  00008D L
    TH2                                                         =  0000CD L
    TI                                                          =  000099 L
    TL0                                                         =  00008A L
    TL1                                                         =  00008B L
    TL2                                                         =  0000CC L
    TMOD                                                        =  000089 L
    TR0                                                         =  00008C L
    TR1                                                         =  00008E L
    TR2                                                         =  0000CA L
    TXD                                                         =  0000B1 L
 17 XFmain$HardwareInit$0$0                                     =  00002C GR
 17 XFmain$SoftwareInit$0$0                                     =  000036 GR
 17 XG$VREFConfigInit$0$0                                       =  00006B GR
 17 XG$main$0$0                                                 =  000097 GR
    _ADC_Init                                                      ****** GX
    _AMP_Init                                                      ****** GX
    _CMP3_INT                                                      ****** GX
    _CMP3_Init                                                     ****** GX
    _CMP3_Interrupt_Init                                           ****** GX
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 7.
Hexadecimal [24-Bits]

Symbol Table

    _DRV_ISR                                                       ****** GX
 17 _DebugSet                                                      000000 R
    _Driver_Init                                                   ****** GX
    _EA                                                         =  0000AF G
    _EXTERN0_INT                                                   ****** GX
    _GPIO_Init                                                     ****** GX
    _GetCurrentOffset                                              ****** GX
 17 _HardwareInit                                                  000001 R
    _IP2                                                        =  00008C G
    _LVW_TSD_INT                                                   ****** GX
    _MC_Control                                                    ****** GX
    _MotorcontrolInit                                              ****** GX
    _SYStick_INT                                                   ****** GX
 17 _SoftwareInit                                                  00002D R
    _TIM2_INT                                                      ****** GX
    _TIM3_INT                                                      ****** GX
    _TSD_Init                                                      ****** GX
    _TickCycle_1ms                                                 ****** GX
    _Timer3_Init                                                   ****** GX
 17 _VREFConfigInit                                                000037 GR
    __ftcc_gsinit_startup                                          ****** GX
  E __ftcc_program_startup                                         000066 GR
  E __interrupt_vect                                               000000 R
    __mcs51_genRAMCLEAR                                            ****** GX
    __mcs51_genXINIT                                               ****** GX
    __mcs51_genXRAMCLEAR                                           ****** GX
  6 __start__stack                                                 000000 GR
  5 _g_1mTick                                                      000000 GR
 17 _main                                                          00006C GR
    _mcCurOffset                                                   ****** GX
    _mcFaultSource                                                 ****** GX
    _mcState                                                       ****** GX
    a                                                           =  0000E0 L
    a.0                                                         =  0000E0 L
    a.1                                                         =  0000E1 L
    a.2                                                         =  0000E2 L
    a.3                                                         =  0000E3 L
    a.4                                                         =  0000E4 L
    a.5                                                         =  0000E5 L
    a.6                                                         =  0000E6 L
    a.7                                                         =  0000E7 L
    ac                                                          =  0000D6 L
    acc                                                         =  0000E0 L
    acc.0                                                       =  0000E0 L
    acc.1                                                       =  0000E1 L
    acc.2                                                       =  0000E2 L
    acc.3                                                       =  0000E3 L
    acc.4                                                       =  0000E4 L
    acc.5                                                       =  0000E5 L
    acc.6                                                       =  0000E6 L
    acc.7                                                       =  0000E7 L
    ar0                                                         =  000000 
    ar1                                                         =  000001 
    ar2                                                         =  000002 
    ar3                                                         =  000003 
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 8.
Hexadecimal [24-Bits]

Symbol Table

    ar4                                                         =  000004 
    ar5                                                         =  000005 
    ar6                                                         =  000006 
    ar7                                                         =  000007 
    b                                                           =  0000F0 L
    b.0                                                         =  0000F0 L
    b.1                                                         =  0000F1 L
    b.2                                                         =  0000F2 L
    b.3                                                         =  0000F3 L
    b.4                                                         =  0000F4 L
    b.5                                                         =  0000F5 L
    b.6                                                         =  0000F6 L
    b.7                                                         =  0000F7 L
    cprl2                                                       =  0000C8 L
    ct2                                                         =  0000C9 L
    cy                                                          =  0000D7 L
    dph                                                         =  000083 L
    dpl                                                         =  000082 L
    ea                                                          =  0000AF L
    es                                                          =  0000AC L
    et0                                                         =  0000A9 L
    et1                                                         =  0000AB L
    et2                                                         =  0000AD L
    ex0                                                         =  0000A8 L
    ex1                                                         =  0000AA L
    exen2                                                       =  0000CB L
    exf2                                                        =  0000CE L
    f0                                                          =  0000D5 L
    ie                                                          =  0000A8 L
    ie.0                                                        =  0000A8 L
    ie.1                                                        =  0000A9 L
    ie.2                                                        =  0000AA L
    ie.3                                                        =  0000AB L
    ie.4                                                        =  0000AC L
    ie.5                                                        =  0000AD L
    ie.7                                                        =  0000AF L
    ie0                                                         =  000089 L
    ie1                                                         =  00008B L
    int0                                                        =  0000B2 L
    int1                                                        =  0000B3 L
    ip                                                          =  0000B8 L
    ip.0                                                        =  0000B8 L
    ip.1                                                        =  0000B9 L
    ip.2                                                        =  0000BA L
    ip.3                                                        =  0000BB L
    ip.4                                                        =  0000BC L
    ip.5                                                        =  0000BD L
    it0                                                         =  000088 L
    it1                                                         =  00008A L
    ov                                                          =  0000D2 L
    p                                                           =  0000D0 L
    p0                                                          =  000080 L
    p0.0                                                        =  000080 L
    p0.1                                                        =  000081 L
    p0.2                                                        =  000082 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 9.
Hexadecimal [24-Bits]

Symbol Table

    p0.3                                                        =  000083 L
    p0.4                                                        =  000084 L
    p0.5                                                        =  000085 L
    p0.6                                                        =  000086 L
    p0.7                                                        =  000087 L
    p1                                                          =  000090 L
    p1.0                                                        =  000090 L
    p1.1                                                        =  000091 L
    p1.2                                                        =  000092 L
    p1.3                                                        =  000093 L
    p1.4                                                        =  000094 L
    p1.5                                                        =  000095 L
    p1.6                                                        =  000096 L
    p1.7                                                        =  000097 L
    p2                                                          =  0000A0 L
    p2.0                                                        =  0000A0 L
    p2.1                                                        =  0000A1 L
    p2.2                                                        =  0000A2 L
    p2.3                                                        =  0000A3 L
    p2.4                                                        =  0000A4 L
    p2.5                                                        =  0000A5 L
    p2.6                                                        =  0000A6 L
    p2.7                                                        =  0000A7 L
    p3                                                          =  0000B0 L
    p3.0                                                        =  0000B0 L
    p3.1                                                        =  0000B1 L
    p3.2                                                        =  0000B2 L
    p3.3                                                        =  0000B3 L
    p3.4                                                        =  0000B4 L
    p3.5                                                        =  0000B5 L
    p3.6                                                        =  0000B6 L
    p3.7                                                        =  0000B7 L
    pcon                                                        =  000087 L
    ps                                                          =  0000BC L
    psw                                                         =  0000D0 L
    psw.0                                                       =  0000D0 L
    psw.1                                                       =  0000D1 L
    psw.2                                                       =  0000D2 L
    psw.3                                                       =  0000D3 L
    psw.4                                                       =  0000D4 L
    psw.5                                                       =  0000D5 L
    psw.6                                                       =  0000D6 L
    psw.7                                                       =  0000D7 L
    pt0                                                         =  0000B9 L
    pt1                                                         =  0000BB L
    pt2                                                         =  0000BD L
    px0                                                         =  0000B8 L
    px1                                                         =  0000BA L
    rb8                                                         =  00009A L
    rcap2h                                                      =  0000CB L
    rcap2l                                                      =  0000CA L
    rclk                                                        =  0000CD L
    ren                                                         =  00009C L
    ri                                                          =  000098 L
    rs0                                                         =  0000D3 L
ASxxxx Assembler V1.0.2.1  (Intel 8051), page 10.
Hexadecimal [24-Bits]

Symbol Table

    rs1                                                         =  0000D4 L
    rxd                                                         =  0000B0 L
    sbuf                                                        =  000099 L
    scon                                                        =  000098 L
    scon.0                                                      =  000098 L
    scon.1                                                      =  000099 L
    scon.2                                                      =  00009A L
    scon.3                                                      =  00009B L
    scon.4                                                      =  00009C L
    scon.5                                                      =  00009D L
    scon.6                                                      =  00009E L
    scon.7                                                      =  00009F L
    sm0                                                         =  00009F L
    sm1                                                         =  00009E L
    sm2                                                         =  00009D L
    sp                                                          =  000081 L
    t2con                                                       =  0000C8 L
    t2con.0                                                     =  0000C8 L
    t2con.1                                                     =  0000C9 L
    t2con.2                                                     =  0000CA L
    t2con.3                                                     =  0000CB L
    t2con.4                                                     =  0000CC L
    t2con.5                                                     =  0000CD L
    t2con.6                                                     =  0000CE L
    t2con.7                                                     =  0000CF L
    tb8                                                         =  00009B L
    tclk                                                        =  0000CC L
    tcon                                                        =  000088 L
    tcon.0                                                      =  000088 L
    tcon.1                                                      =  000089 L
    tcon.2                                                      =  00008A L
    tcon.3                                                      =  00008B L
    tcon.4                                                      =  00008C L
    tcon.5                                                      =  00008D L
    tcon.6                                                      =  00008E L
    tcon.7                                                      =  00008F L
    tf0                                                         =  00008D L
    tf1                                                         =  00008F L
    tf2                                                         =  0000CF L
    th0                                                         =  00008C L
    th1                                                         =  00008D L
    th2                                                         =  0000CD L
    ti                                                          =  000099 L
    tl0                                                         =  00008A L
    tl1                                                         =  00008B L
    tl2                                                         =  0000CC L
    tmod                                                        =  000089 L
    tr0                                                         =  00008C L
    tr1                                                         =  00008E L
    tr2                                                         =  0000CA L
    txd                                                         =  0000B1 L

ASxxxx Assembler V1.0.2.1  (Intel 8051), page 11.
Hexadecimal [24-Bits]

Area Table

   0 _CODE                                      size      0   flags    0
   1 RSEG                                       size      0   flags    8
   2 RSEG0                                      size      0   flags    8
   3 RSEG1                                      size      0   flags    8
   4 REG_BANK_0                                 size      8   flags    4
   5 DSEG                                       size      1   flags    0
   6 SSEG                                       size      1   flags    0
   7 ISEG                                       size      0   flags    0
   8 IABS                                       size      0   flags    8
   9 BSEG                                       size      0   flags   80
   A PSEG                                       size      0   flags   50
   B XSEG                                       size      0   flags   40
   C XABS                                       size      0   flags   48
   D XISEG                                      size      0   flags   40
   E HOME                                       size     69   flags   20
   F GSINIT0                                    size      0   flags   20
  10 GSINIT1                                    size      0   flags   20
  11 GSINIT2                                    size      0   flags   20
  12 GSINIT3                                    size      0   flags   20
  13 GSINIT4                                    size      0   flags   20
  14 GSINIT5                                    size      0   flags   20
  15 GSINIT                                     size      3   flags   20
  16 GSFINAL                                    size      3   flags   20
  17 CSEG                                       size     99   flags   20
  18 CONST                                      size      0   flags   20
  19 XINIT                                      size      0   flags   20
  1A CABS                                       size      0   flags   28

