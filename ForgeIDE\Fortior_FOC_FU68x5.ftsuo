<?xml version="1.0" encoding="ISO-8859-2"?>
<UserOptions>
	<OpenState>
		<OpenFiles>
			Path="..\\User\\include\\Protect.h"
			Path="..\\User\\include\\Customer.h"
			State="490,27378,27378"
		</OpenFiles>
		<OpenFolders>
			<{A5245B62-3438-44e1-B919-7F50790173FD}>
				Expanded="TRUE"
				Name="Application"
				Name="Function"
				Name="HardWare"
				Name="FU65XX_Hardware_Driver"
				Name="Include"
			</{A5245B62-3438-44e1-B919-7F50790173FD}>
		</OpenFolders>
	</OpenState>
	<UI_dockbar_disassembler>
		AddressList=""
	</UI_dockbar_disassembler>
	<UI_dockbar_memory1>
		ViewAddress="$pc"
		AddressList=""
	</UI_dockbar_memory1>
	<UI_dockbar_memory2>
		ViewAddress="$pc"
		AddressList=""
	</UI_dockbar_memory2>
	<UI_dockbar_memory3>
		ViewAddress="$pc"
		AddressList=""
	</UI_dockbar_memory3>
	<UI_dockbar_memory4>
		ViewAddress="$pc"
		AddressList=""
	</UI_dockbar_memory4>
	<UI_dockbar_registers>
		ActiveRegGroup="General Register"
	</UI_dockbar_registers>
	<WatchVariables>
		<dockbar_watch1>
			<Variable>
				Name="mcFocCtrl"
			</Variable>
			<Variable>
				Name="mcRefRamp"
			</Variable>
			<Variable>
				Name="mcCurOffset"
			</Variable>
			<Variable>
				Name="mcState"
			</Variable>
			<Variable>
				Name="mcFaultSource"
			</Variable>
		</dockbar_watch1>
	</WatchVariables>
	IDEVersion="v1.0.2.250725"
</UserOptions>
