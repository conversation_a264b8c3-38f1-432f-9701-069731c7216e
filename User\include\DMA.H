#ifndef __DMA_H__
#define __DMA_H__

#include <FU65xx_2.h>

/*************************************************************************************/

typedef enum
{
    UART_DRAM  = 0,
    DRAM_UART  = DMACFG0,
    I2C_DRAM   = DMACFG1,
    DRAM_I2C   = DMACFG1 | DMACFG0,
    SPI_DRAM   = DMACFG2,
    DRAM_SPI   = DMACFG2 | DMACFG0,
    UART2_DRAM = DMACFG2 | DMACFG1,
    DRAM_UART2 = DMACFG2 | DMACFG1 | DMACFG0
}eType_DMA_PIPE;

typedef enum
{
    DMA_IRQ_L1 = 0,
    DMA_IRQ_L2 = 0x40,
    DMA_IRQ_L3 = 0x80,
    DMA_IRQ_L4 = 0xc0
}eType_DMA_IRQ;

typedef enum
{
    BIG_ENDIAN    = 0,
    LITTLE_ENDIAN = ENDIAN
}eType_DMA_Endian;

typedef enum
{
    DMA_BSY = DMABSY,
    DMA_IRQ = DMAIF
}eType_DMA_Statu;

/******************************************************************************///Function Subject
extern void SetPipe_DMA0(eType_DMA_PIPE ePipe);
extern void SetPipe_DMA1(eType_DMA_PIPE ePipe);
extern void SetDataPackage_DMA0(unsigned short ulAddr, char cLen);
extern void SetDataPackage_DMA1(unsigned short ulAddr, char cLen);
extern void EnableRun_DMA0(void);
extern void EnableRun_DMA1(void);
extern bool GetStatus_DMA0(eType_DMA_Statu eStatu);
extern bool GetStatus_DMA1(eType_DMA_Statu eStatu);
extern void SetEndian_DMA(eType_DMA_Endian eEndian);
extern void SetIRQ_DMA(ebool eIRQ, eType_DMA_IRQ eIP);
extern void SetDbgMod_DMA(void);
extern void SetDbgData_DMA(unsigned short ulAddr);
extern void Set_DBG_DMA(uint16 DMAAddr);

#endif






































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































