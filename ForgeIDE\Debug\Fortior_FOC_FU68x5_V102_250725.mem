Internal RAM layout:
      0 1 2 3 4 5 6 7 8 9 A B C D E F
0x00:|0|0|0|0|0|0|0|0|a|c|d|e|e|e|e|e|
0x10:|e|e|g|Q|Q|Q| | | | | | | | | | |
0x20:|B|T|b|b|b|b|b|b|b|b|b|b|b|b|b|b|
0x30:|b|b|b|b|b|b|b|b|b|b|b|b|b|b|f|f|
0x40:|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|
0x50:|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|
0x60:|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|
0x70:|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|f|
0x80:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0x90:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0xa0:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0xb0:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0xc0:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0xd0:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0xe0:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0xf0:|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|S|
0-3:Reg Banks, T:Bit regs, a-z:Data, B:Bits, Q:Overlay, I:iData, S:Stack, A:Absolute

Stack starts at: 0x80 (sp set to 0x7f) with 128 bytes available.
The largest spare internal RAM space starts at 0x16 with 10 bytes available.

Memory summary:
   Name             Start    End      Size     Max     
   ---------------- -------- -------- -------- --------
   DATA RAM           0x08     0x7f     105      120   
   IDATA RAM          0x00     0x00       0      248   
   EXTERNAL RAM     0x0000   0x0155     342     3831   
   ROM/EPROM/FLASH  0x0000   0x2e38   11833    32768   
